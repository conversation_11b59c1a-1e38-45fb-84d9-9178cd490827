export interface Course {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: number; // 课程时长(分钟)
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  coachId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CoursePackage {
  id: string;
  name: string;
  description: string;
  coachId: string;
  totalSessions: number; // 总课时数
  usedSessions: number; // 已使用课时数
  price: number;
  originalPrice: number; // 原价
  validityDays: number; // 有效期(天)
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  features: string[]; // 课程包特色
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // 关联数据
  coach?: {
    id: string;
    name: string;
    avatar?: string;
    rating: number;
  };
}

export interface CourseCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
