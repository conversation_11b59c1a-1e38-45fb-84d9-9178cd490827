# 🚀 Shuan-Q 移动端快速启动指南

## 📋 环境要求

### 系统要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **React Native CLI**: 最新版本
- **Android Studio**: (Android开发)
- **Xcode**: (iOS开发，仅macOS)

### 开发工具推荐
- **VS Code**: 推荐的代码编辑器
- **React Native Debugger**: 调试工具
- **Flipper**: Facebook的移动应用调试平台

## 🛠️ 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd Shuan-Q
```

### 2. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装移动端依赖
cd mobile-app
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 3. 安装React Native依赖
```bash
cd ../mobile-app

# iOS依赖 (仅macOS)
cd ios && pod install && cd ..

# Android依赖会在构建时自动安装
```

## 🏃‍♂️ 运行项目

### 启动后端服务
```bash
# 在项目根目录
cd backend
npm run dev

# 或者使用测试服务器
NODE_ENV=test node test-server.js
```

### 启动移动端应用

#### Android
```bash
cd mobile-app

# 启动Metro bundler
npx react-native start

# 在新终端运行Android应用
npx react-native run-android
```

#### iOS (仅macOS)
```bash
cd mobile-app

# 启动Metro bundler
npx react-native start

# 在新终端运行iOS应用
npx react-native run-ios
```

## 📱 项目结构说明

### 核心目录
```
mobile-app/
├── src/
│   ├── components/     # 通用UI组件
│   ├── screens/        # 页面组件
│   ├── navigation/     # 导航配置
│   ├── store/          # Redux状态管理
│   ├── services/       # API服务
│   └── utils/          # 工具函数
├── android/            # Android原生代码
├── ios/                # iOS原生代码
└── package.json        # 依赖配置
```

### 主要页面
- **首页**: `src/screens/home/<USER>
- **商城**: `src/screens/mall/MallScreen.tsx`
- **教练**: `src/screens/coach/CoachListScreen.tsx`
- **个人中心**: `src/screens/profile/ProfileScreen.tsx`

## 🔧 开发配置

### 环境变量
创建 `.env` 文件：
```env
API_BASE_URL=http://localhost:3000/api
DEBUG_MODE=true
```

### TypeScript配置
项目已配置TypeScript，确保类型安全：
- 导航类型: `src/navigation/types.ts`
- API类型: `shared/types/`
- 组件Props类型: 各组件文件中定义

### 状态管理
使用Redux Toolkit进行状态管理：
```typescript
// 使用示例
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { fetchPackages } from '../store/slices/packageSlice';

const MyComponent = () => {
  const dispatch = useDispatch();
  const { packages, loading } = useSelector((state: RootState) => state.packages);
  
  useEffect(() => {
    dispatch(fetchPackages());
  }, []);
};
```

## 🎨 UI组件使用

### 通用组件
```typescript
// 搜索栏
import SearchBar from '../components/SearchBar';
<SearchBar placeholder="搜索..." onSearch={handleSearch} />

// 轮播图
import Banner from '../components/Banner';
<Banner data={bannerData} autoPlay={true} />

// 商品卡片
import ProductCard from '../components/ProductCard';
<ProductCard package={packageData} onPress={handlePress} />
```

### 导航使用
```typescript
// 页面跳转
import { useNavigation } from '@react-navigation/native';

const navigation = useNavigation();
navigation.navigate('PackageDetail', { packageId: '123' });

// 获取路由参数
import { useRoute } from '@react-navigation/native';

const route = useRoute();
const { packageId } = route.params;
```

## 🔍 调试技巧

### React Native Debugger
1. 安装React Native Debugger
2. 在模拟器中按 `Cmd+D` (iOS) 或 `Cmd+M` (Android)
3. 选择 "Debug JS Remotely"

### 日志调试
```typescript
// 使用console.log进行调试
console.log('Debug info:', data);

// 使用Flipper进行网络请求调试
// 自动集成，无需额外配置
```

### 常见问题解决

#### Metro bundler启动失败
```bash
# 清理缓存
npx react-native start --reset-cache

# 清理node_modules
rm -rf node_modules && npm install
```

#### Android构建失败
```bash
# 清理Android构建
cd android && ./gradlew clean && cd ..

# 重新构建
npx react-native run-android
```

#### iOS构建失败
```bash
# 清理iOS构建
cd ios && xcodebuild clean && cd ..

# 重新安装pods
cd ios && pod install && cd ..
```

## 📊 API集成

### 后端API地址
- **开发环境**: `http://localhost:3000/api`
- **测试环境**: 配置在环境变量中

### 主要API端点
- **认证**: `/auth/login`, `/auth/send-code`
- **教练**: `/coaches`, `/coaches/:id`
- **课程包**: `/packages`, `/packages/:id`
- **订单**: `/orders`, `/orders/:id`
- **预约**: `/appointments`

### API调用示例
```typescript
// 使用Redux Thunk
import { fetchPackages } from '../store/slices/packageSlice';

dispatch(fetchPackages({ 
  page: 1, 
  limit: 10, 
  category: 'basic' 
}));
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm test

# API集成测试
cd mobile-app && npm run test
```

### 测试覆盖
- 组件渲染测试
- Redux状态管理测试
- API集成测试
- 导航流程测试

## 📦 构建发布

### Android APK
```bash
cd android
./gradlew assembleRelease
```

### iOS IPA
```bash
# 在Xcode中选择Product -> Archive
# 或使用命令行
xcodebuild -workspace ios/ShuanQ.xcworkspace -scheme ShuanQ archive
```

## 🔄 持续集成

### GitHub Actions配置
项目包含CI/CD配置文件，支持：
- 自动化测试
- 代码质量检查
- 自动构建
- 部署到测试环境

## 📞 技术支持

### 常用命令
```bash
# 查看设备列表
npx react-native run-android --deviceId

# 查看日志
npx react-native log-android
npx react-native log-ios

# 重置Metro缓存
npx react-native start --reset-cache
```

### 开发资源
- [React Native官方文档](https://reactnative.dev/)
- [Redux Toolkit文档](https://redux-toolkit.js.org/)
- [React Navigation文档](https://reactnavigation.org/)

## 🎯 下一步

1. **熟悉项目结构**: 浏览主要目录和文件
2. **运行演示**: 启动后端和移动端应用
3. **查看功能**: 体验各个页面和功能
4. **阅读代码**: 理解组件和状态管理逻辑
5. **开始开发**: 根据需求添加新功能

祝你开发愉快！🎉
