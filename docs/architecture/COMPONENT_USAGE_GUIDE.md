# 📦 Shuan-Q 组件使用指南

## 🎯 概述

本指南详细介绍了Shuan-Q移动端应用中所有通用组件的使用方法、属性配置和最佳实践。

## 🔍 SearchBar 搜索栏组件

### 基本用法
```typescript
import SearchBar from '../components/SearchBar';

<SearchBar
  placeholder="搜索课程包、教练..."
  onSearch={handleSearch}
  value={searchQuery}
  onFocus={handleFocus}
  editable={true}
/>
```

### 属性说明
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| placeholder | string | '搜索...' | 占位符文本 |
| onSearch | (query: string) => void | - | 搜索回调函数 |
| onFocus | () => void | - | 获取焦点回调 |
| value | string | - | 受控输入值 |
| editable | boolean | true | 是否可编辑 |

### 使用场景
- 首页全局搜索
- 商城商品搜索
- 教练搜索页面
- 任何需要搜索功能的页面

## 🎠 Banner 轮播图组件

### 基本用法
```typescript
import Banner from '../components/Banner';

const bannerData = [
  {
    id: '1',
    image: 'https://example.com/image1.jpg',
    title: '专业台球教学',
    subtitle: '一对一精品课程',
    onPress: () => navigation.navigate('Detail')
  }
];

<Banner
  data={bannerData}
  autoPlay={true}
  autoPlayInterval={3000}
/>
```

### 属性说明
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | BannerItem[] | - | 轮播数据数组 |
| autoPlay | boolean | true | 是否自动播放 |
| autoPlayInterval | number | 3000 | 自动播放间隔(ms) |

### BannerItem 数据结构
```typescript
interface BannerItem {
  id: string;
  image: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
}
```

## ⚡ QuickActions 快速入口组件

### 基本用法
```typescript
import QuickActions from '../components/QuickActions';

const quickActions = [
  {
    id: '1',
    icon: 'person',
    title: '找教练',
    subtitle: '专业教练',
    onPress: () => navigation.navigate('Coach')
  }
];

<QuickActions
  data={quickActions}
  numColumns={4}
/>
```

### 属性说明
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | QuickActionItem[] | - | 快速入口数据 |
| numColumns | number | 4 | 每行显示数量 |

## 🛍️ ProductCard 商品卡片组件

### 基本用法
```typescript
import ProductCard from '../components/ProductCard';

<ProductCard
  package={packageData}
  onPress={() => navigation.navigate('PackageDetail', { packageId: packageData.id })}
/>
```

### Package 数据结构
```typescript
interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  totalSessions: number;
  validityDays: number;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  coach?: {
    user?: {
      nickname: string;
    };
  };
}
```

### 特性
- 自动显示级别标签（初级/中级/高级）
- 支持原价和现价对比
- 显示课时数和有效期
- 教练信息展示

## 👨‍🏫 CoachCard 教练卡片组件

### 基本用法
```typescript
import CoachCard from '../components/CoachCard';

<CoachCard
  coach={coachData}
  onPress={() => navigation.navigate('CoachDetail', { coachId: coachData.id })}
/>
```

### Coach 数据结构
```typescript
interface Coach {
  id: string;
  user?: {
    nickname: string;
  };
  specialties?: string[];
  experience?: number;
  rating?: number;
  hourlyRate?: number;
  bio?: string;
}
```

### 特性
- 显示教练头像（默认图标）
- 评分和经验年限
- 专长标签展示
- 小时费率显示

## 🏷️ CategorySelector 分类选择器

### 基本用法
```typescript
import CategorySelector from '../components/CategorySelector';

const categories = [
  { id: 'all', name: '全部', icon: 'apps' },
  { id: 'basic', name: '基础教学', icon: 'school' }
];

<CategorySelector
  categories={categories}
  selectedCategory={selectedCategory}
  onCategorySelect={handleCategorySelect}
/>
```

### Category 数据结构
```typescript
interface Category {
  id: string;
  name: string;
  icon: string; // Material Icons 图标名
}
```

### 特性
- 水平滚动选择
- 图标和文字展示
- 选中状态高亮
- 响应式设计

## 🔽 FilterModal 筛选弹窗组件

### 基本用法
```typescript
import FilterModal from '../components/FilterModal';

<FilterModal
  visible={showFilter}
  filters={currentFilters}
  onApply={handleFilterApply}
  onClose={() => setShowFilter(false)}
/>
```

### Filters 数据结构
```typescript
interface Filters {
  level: string;
  priceRange: string;
  sortBy: string;
  sortOrder: string;
}
```

### 特性
- 底部弹出式设计
- 多种筛选选项
- 重置功能
- 应用筛选确认

## 🎨 样式规范

### 颜色系统
```typescript
const colors = {
  primary: '#5a9178',      // 主色调
  secondary: '#f5222d',    // 价格红
  success: '#52c41a',      // 成功绿
  warning: '#faad14',      // 警告黄
  text: '#333',            // 主文字
  textSecondary: '#666',   // 次要文字
  textLight: '#999',       // 浅色文字
  background: '#f8f9fa',   // 背景色
  white: '#fff',           // 白色
  border: '#e1e1e1'        // 边框色
};
```

### 间距系统
```typescript
const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24
};
```

### 字体大小
```typescript
const fontSize = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 28
};
```

## 📱 响应式设计

### 屏幕适配
```typescript
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// 卡片宽度计算
const CARD_WIDTH = (width - 48) / 2; // 16px margin + 16px gap
```

### 安全区域
```typescript
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const insets = useSafeAreaInsets();
const paddingTop = insets.top;
```

## 🔧 最佳实践

### 1. 组件复用
- 优先使用通用组件
- 保持组件的单一职责
- 通过props传递配置

### 2. 性能优化
```typescript
// 使用React.memo优化渲染
const ProductCard = React.memo(({ package, onPress }) => {
  // 组件实现
});

// 使用useCallback优化回调
const handlePress = useCallback(() => {
  navigation.navigate('Detail', { id });
}, [id]);
```

### 3. 类型安全
```typescript
// 定义清晰的Props接口
interface ProductCardProps {
  package: Package;
  onPress: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ package, onPress }) => {
  // 组件实现
};
```

### 4. 错误处理
```typescript
// 组件内部错误边界
const ProductCard = ({ package, onPress }) => {
  if (!package) {
    return <EmptyCard />;
  }
  
  return (
    // 正常渲染
  );
};
```

## 🚀 扩展指南

### 创建新组件
1. 在 `src/components/` 目录创建新文件
2. 定义TypeScript接口
3. 实现组件逻辑
4. 添加样式
5. 导出组件

### 组件模板
```typescript
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface MyComponentProps {
  title: string;
  onPress?: () => void;
}

const MyComponent: React.FC<MyComponentProps> = ({ title, onPress }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
});

export default MyComponent;
```

## 📚 参考资源

- [React Native组件文档](https://reactnative.dev/docs/components-and-apis)
- [Material Icons图标库](https://fonts.google.com/icons)
- [React Navigation文档](https://reactnavigation.org/)
- [Redux Toolkit文档](https://redux-toolkit.js.org/)

通过遵循这些指南，你可以高效地使用和扩展Shuan-Q应用的组件系统！🎉
