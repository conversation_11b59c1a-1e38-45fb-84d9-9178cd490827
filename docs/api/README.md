# Shuan-Q API 文档

## 基础信息
- 基础URL: `http://localhost:3000/api`
- 认证方式: JWT Token
- 请求格式: JSON
- 响应格式: JSON

## 认证接口

### 发送验证码
```
POST /auth/send-code
```

请求体:
```json
{
  "phone": "13800138000"
}
```

响应:
```json
{
  "success": true,
  "message": "验证码已发送"
}
```

### 登录
```
POST /auth/login
```

请求体:
```json
{
  "phone": "13800138000",
  "code": "123456"
}
```

响应:
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "phone": "13800138000",
      "nickname": "用户昵称",
      "userType": "student"
    }
  }
}
```

## 教练接口

### 获取教练列表
```
GET /coaches
```

查询参数:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `location`: 地区筛选
- `specialty`: 专长筛选

响应:
```json
{
  "success": true,
  "data": {
    "coaches": [
      {
        "id": "coach-id",
        "name": "张教练",
        "avatar": "avatar-url",
        "rating": 4.9,
        "ratingCount": 120,
        "specialties": ["斯诺克", "九球"],
        "location": "北京市朝阳区",
        "hourlyRate": 200
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

### 获取教练详情
```
GET /coaches/:id
```

响应:
```json
{
  "success": true,
  "data": {
    "id": "coach-id",
    "name": "张教练",
    "avatar": "avatar-url",
    "description": "教练简介",
    "experience": "教学经验",
    "rating": 4.9,
    "ratingCount": 120,
    "specialties": ["斯诺克", "九球"],
    "location": "北京市朝阳区",
    "hourlyRate": 200,
    "packages": [
      {
        "id": "package-id",
        "title": "初级课程包",
        "price": 800,
        "sessions": 4
      }
    ]
  }
}
```

## 预约接口

### 创建预约
```
POST /appointments
```

请求头:
```
Authorization: Bearer jwt-token
```

请求体:
```json
{
  "coachId": "coach-id",
  "packageId": "package-id",
  "date": "2024-01-15",
  "startTime": "14:00",
  "endTime": "15:00",
  "notes": "备注信息"
}
```

响应:
```json
{
  "success": true,
  "data": {
    "id": "appointment-id",
    "status": "pending",
    "date": "2024-01-15",
    "startTime": "14:00",
    "endTime": "15:00"
  }
}
```

### 获取我的预约
```
GET /appointments/my
```

请求头:
```
Authorization: Bearer jwt-token
```

响应:
```json
{
  "success": true,
  "data": [
    {
      "id": "appointment-id",
      "coach": {
        "name": "张教练",
        "avatar": "avatar-url"
      },
      "date": "2024-01-15",
      "startTime": "14:00",
      "endTime": "15:00",
      "status": "confirmed",
      "location": "球房地址"
    }
  ]
}
```

## 错误响应格式

```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

常见错误码:
- `INVALID_PHONE`: 手机号格式错误
- `INVALID_CODE`: 验证码错误
- `TOKEN_EXPIRED`: Token已过期
- `UNAUTHORIZED`: 未授权访问
- `NOT_FOUND`: 资源不存在
- `VALIDATION_ERROR`: 参数验证错误
