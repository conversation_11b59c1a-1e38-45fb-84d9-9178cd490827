{"name": "shuan-q-backend", "version": "1.0.0", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["billiards", "coach", "appointment", "api"], "author": "Your Name", "license": "MIT", "description": "Shuan-Q 台球教练预约平台后端API"}