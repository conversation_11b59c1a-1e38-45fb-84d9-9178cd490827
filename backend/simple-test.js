const http = require('http');
const url = require('url');

// 模拟数据
const coaches = [
  {
    id: '1',
    name: '张教练',
    rating: 4.9,
    specialties: ['斯诺克', '九球'],
    location: '北京市朝阳区',
    hourlyRate: 200
  },
  {
    id: '2', 
    name: '李教练',
    rating: 4.7,
    specialties: ['中式八球', '斯诺克'],
    location: '上海市浦东新区',
    hourlyRate: 180
  }
];

const verificationCodes = new Map();
const appointments = [];

const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // 解析请求体
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', () => {
    let requestData = {};
    if (body) {
      try {
        requestData = JSON.parse(body);
      } catch (e) {
        // 忽略解析错误
      }
    }

    res.setHeader('Content-Type', 'application/json');

    // 路由处理
    if (path === '/' && method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        message: 'Shuan-Q API 测试服务器',
        version: '1.0.0',
        status: 'running',
        endpoints: [
          'POST /api/auth/send-code',
          'POST /api/auth/login',
          'GET /api/coaches',
          'GET /api/coaches/:id',
          'POST /api/appointments'
        ]
      }, null, 2));
    }
    
    else if (path === '/api/auth/send-code' && method === 'POST') {
      const { phone } = requestData;
      if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: '请输入有效的手机号'
        }));
        return;
      }
      
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      verificationCodes.set(phone, {
        code,
        expiresAt: Date.now() + 5 * 60 * 1000
      });
      
      console.log(`📱 发送验证码到 ${phone}: ${code}`);
      
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        message: '验证码已发送'
      }));
    }
    
    else if (path === '/api/auth/login' && method === 'POST') {
      const { phone, code } = requestData;
      const stored = verificationCodes.get(phone);
      
      if (!stored || stored.code !== code || Date.now() > stored.expiresAt) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: '验证码错误或已过期'
        }));
        return;
      }
      
      verificationCodes.delete(phone);
      
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          token: `token_${Date.now()}`,
          user: {
            id: `user_${Date.now()}`,
            phone,
            nickname: `用户${phone.slice(-4)}`,
            userType: 'student'
          }
        }
      }));
    }
    
    else if (path === '/api/coaches' && method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          coaches,
          pagination: {
            total: coaches.length,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        }
      }));
    }
    
    else if (path.startsWith('/api/coaches/') && method === 'GET') {
      const id = path.split('/')[3];
      const coach = coaches.find(c => c.id === id);
      
      if (!coach) {
        res.writeHead(404);
        res.end(JSON.stringify({
          success: false,
          error: '教练不存在'
        }));
        return;
      }
      
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: coach
      }));
    }
    
    else if (path === '/api/appointments' && method === 'POST') {
      const { coachId, date, startTime, endTime } = requestData;
      
      const appointment = {
        id: `appointment_${Date.now()}`,
        coachId,
        date,
        startTime,
        endTime,
        status: 'pending',
        createdAt: new Date().toISOString()
      };
      
      appointments.push(appointment);
      
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: appointment
      }));
    }
    
    else {
      res.writeHead(404);
      res.end(JSON.stringify({
        success: false,
        error: '接口不存在'
      }));
    }
  });
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`🚀 Shuan-Q 测试服务器启动成功！`);
  console.log(`📖 API地址: http://localhost:${PORT}`);
  console.log(`\n📱 测试提示: 验证码会在控制台显示`);
  console.log(`\n🧪 可以开始测试以下功能:`);
  console.log(`   1. 发送验证码: POST /api/auth/send-code`);
  console.log(`   2. 用户登录: POST /api/auth/login`);
  console.log(`   3. 教练列表: GET /api/coaches`);
  console.log(`   4. 教练详情: GET /api/coaches/1`);
  console.log(`   5. 创建预约: POST /api/appointments`);
});
