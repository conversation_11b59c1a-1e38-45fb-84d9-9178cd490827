#!/bin/bash

echo "🧪 Shuan-Q API 功能测试"
echo "========================"

BASE_URL="http://localhost:3000"

echo ""
echo "📱 测试1: 发送验证码"
echo "curl -X POST $BASE_URL/api/auth/send-code -H 'Content-Type: application/json' -d '{\"phone\":\"13800138000\"}'"

echo ""
echo "🔐 测试2: 用户登录 (需要先获取验证码)"
echo "curl -X POST $BASE_URL/api/auth/login -H 'Content-Type: application/json' -d '{\"phone\":\"13800138000\",\"code\":\"验证码\"}'"

echo ""
echo "👨‍🏫 测试3: 获取教练列表"
echo "curl -X GET $BASE_URL/api/coaches"

echo ""
echo "📋 测试4: 获取教练详情"
echo "curl -X GET $BASE_URL/api/coaches/1"

echo ""
echo "📅 测试5: 创建预约"
echo "curl -X POST $BASE_URL/api/appointments -H 'Content-Type: application/json' -d '{\"coachId\":\"1\",\"date\":\"2024-01-15\",\"startTime\":\"14:00\",\"endTime\":\"15:00\"}'"

echo ""
echo "🏥 测试6: 健康检查"
echo "curl -X GET $BASE_URL/"

echo ""
echo "请先启动服务器，然后运行上述命令进行测试"
