const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const users = [];
const coaches = [
  {
    id: '1',
    name: '张教练',
    avatar: 'https://example.com/avatar1.jpg',
    rating: 4.9,
    ratingCount: 120,
    specialties: ['斯诺克', '九球'],
    location: '北京市朝阳区',
    hourlyRate: 200,
    description: '专业台球教练，10年教学经验',
    experience: '曾获得全国台球比赛冠军'
  },
  {
    id: '2',
    name: '李教练',
    avatar: 'https://example.com/avatar2.jpg',
    rating: 4.7,
    ratingCount: 85,
    specialties: ['中式八球', '斯诺克'],
    location: '上海市浦东新区',
    hourlyRate: 180,
    description: '耐心细致的教学风格',
    experience: '5年专业教学经验'
  }
];

const appointments = [];

// 验证码存储
const verificationCodes = new Map();

// 工具函数
const generateCode = () => {
  // 测试环境使用固定验证码
  if (process.env.NODE_ENV === 'test') {
    return '123456';
  }
  return Math.floor(100000 + Math.random() * 900000).toString();
};
const generateToken = (userId) => `token_${userId}_${Date.now()}`;

// 认证路由
app.post('/api/auth/send-code', (req, res) => {
  const { phone } = req.body;
  
  if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
    return res.status(400).json({
      success: false,
      error: '请输入有效的手机号',
      code: 'INVALID_PHONE'
    });
  }
  
  const code = generateCode();
  verificationCodes.set(phone, {
    code,
    expiresAt: Date.now() + 5 * 60 * 1000
  });
  
  console.log(`�� 发送验证码到 ${phone}: ${code}`);
  
  res.json({
    success: true,
    message: '验证码已发送'
  });
});

app.post('/api/auth/login', (req, res) => {
  const { phone, code } = req.body;
  
  if (!phone || !code) {
    return res.status(400).json({
      success: false,
      error: '手机号和验证码不能为空',
      code: 'MISSING_PARAMS'
    });
  }
  
  const stored = verificationCodes.get(phone);
  if (!stored || stored.code !== code || Date.now() > stored.expiresAt) {
    return res.status(400).json({
      success: false,
      error: '验证码错误或已过期',
      code: 'INVALID_CODE'
    });
  }
  
  // 查找或创建用户
  let user = users.find(u => u.phone === phone);
  if (!user) {
    user = {
      id: `user_${Date.now()}`,
      phone,
      nickname: `用户${phone.slice(-4)}`,
      userType: 'student',
      avatar: null
    };
    users.push(user);
  }
  
  verificationCodes.delete(phone);
  const token = generateToken(user.id);
  
  res.json({
    success: true,
    data: {
      token,
      user
    }
  });
});

// 教练路由
app.get('/api/coaches', (req, res) => {
  const { page = 1, limit = 10, location, specialty } = req.query;
  
  let filteredCoaches = [...coaches];
  
  if (location) {
    filteredCoaches = filteredCoaches.filter(coach => 
      coach.location.includes(location)
    );
  }
  
  if (specialty) {
    filteredCoaches = filteredCoaches.filter(coach => 
      coach.specialties.some(s => s.includes(specialty))
    );
  }
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedCoaches = filteredCoaches.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      coaches: paginatedCoaches,
      pagination: {
        total: filteredCoaches.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredCoaches.length / limit)
      }
    }
  });
});

app.get('/api/coaches/:id', (req, res) => {
  const { id } = req.params;
  const coach = coaches.find(c => c.id === id);
  
  if (!coach) {
    return res.status(404).json({
      success: false,
      error: '教练不存在',
      code: 'COACH_NOT_FOUND'
    });
  }
  
  res.json({
    success: true,
    data: coach
  });
});

// 预约路由
app.post('/api/appointments', (req, res) => {
  const { coachId, date, startTime, endTime, notes } = req.body;
  
  if (!coachId || !date || !startTime || !endTime) {
    return res.status(400).json({
      success: false,
      error: '缺少必要参数',
      code: 'MISSING_PARAMS'
    });
  }
  
  const coach = coaches.find(c => c.id === coachId);
  if (!coach) {
    return res.status(404).json({
      success: false,
      error: '教练不存在',
      code: 'COACH_NOT_FOUND'
    });
  }
  
  const appointment = {
    id: `appointment_${Date.now()}`,
    studentId: 'current_user_id',
    coachId,
    date,
    startTime,
    endTime,
    status: 'pending',
    notes,
    createdAt: new Date().toISOString()
  };
  
  appointments.push(appointment);
  
  res.json({
    success: true,
    data: appointment
  });
});

app.get('/api/appointments/my', (req, res) => {
  res.json({
    success: true,
    data: {
      appointments: appointments.map(apt => ({
        ...apt,
        coach: coaches.find(c => c.id === apt.coachId)
      })),
      pagination: {
        total: appointments.length,
        page: 1,
        limit: 10,
        totalPages: 1
      }
    }
  });
});

// 课程包相关数据
let packages = [
  {
    id: 'pkg-001',
    name: '台球基础入门课程包',
    description: '适合零基础学员的台球入门课程，包含基本姿势、瞄准技巧等',
    coachId: 'coach-001',
    totalSessions: 10,
    usedSessions: 0,
    price: 1500,
    originalPrice: 2000,
    validityDays: 60,
    level: 'beginner',
    category: '基础入门',
    features: [
      '一对一专业指导',
      '基础姿势纠正',
      '瞄准技巧训练',
      '课后练习指导'
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'pkg-002',
    name: '台球技巧提升课程包',
    description: '适合有一定基础的学员，提升台球技巧和战术水平',
    coachId: 'coach-002',
    totalSessions: 8,
    usedSessions: 0,
    price: 2000,
    originalPrice: 2500,
    validityDays: 45,
    level: 'intermediate',
    category: '技巧提升',
    features: [
      '高级技巧训练',
      '战术分析',
      '比赛模拟',
      '个性化指导'
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

let orders = [];

// 课程包API
app.get('/api/packages', (req, res) => {
  const { page = 1, limit = 10, category, level, coachId } = req.query;
  let filteredPackages = packages.filter(pkg => pkg.isActive);

  if (category) {
    filteredPackages = filteredPackages.filter(pkg => pkg.category === category);
  }
  if (level) {
    filteredPackages = filteredPackages.filter(pkg => pkg.level === level);
  }
  if (coachId) {
    filteredPackages = filteredPackages.filter(pkg => pkg.coachId === coachId);
  }

  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedPackages = filteredPackages.slice(startIndex, endIndex);

  // 添加教练信息
  const packagesWithCoach = paginatedPackages.map(pkg => ({
    ...pkg,
    coach: coaches.find(c => c.id === pkg.coachId)
  }));

  res.json({
    success: true,
    data: {
      packages: packagesWithCoach,
      pagination: {
        total: filteredPackages.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredPackages.length / limit)
      }
    }
  });
});

app.get('/api/packages/:id', (req, res) => {
  const { id } = req.params;
  const package = packages.find(pkg => pkg.id === id && pkg.isActive);

  if (!package) {
    return res.status(404).json({
      success: false,
      error: '课程包不存在',
      code: 'PACKAGE_NOT_FOUND'
    });
  }

  const packageWithCoach = {
    ...package,
    coach: coaches.find(c => c.id === package.coachId)
  };

  res.json({
    success: true,
    data: packageWithCoach
  });
});

// 订单API
app.post('/api/orders', (req, res) => {
  const { packageId, paymentMethod = 'wechat' } = req.body;

  const package = packages.find(pkg => pkg.id === packageId && pkg.isActive);
  if (!package) {
    return res.status(404).json({
      success: false,
      error: '课程包不存在',
      code: 'PACKAGE_NOT_FOUND'
    });
  }

  const orderNumber = `SQ${Date.now()}${Math.random().toString(36).substring(2, 8)}`.toUpperCase();
  const order = {
    id: `order-${Date.now()}`,
    orderNumber,
    userId: 'user-001',
    packageId,
    amount: package.price,
    originalAmount: package.originalPrice,
    discountAmount: package.originalPrice - package.price,
    status: 'pending',
    paymentMethod,
    expiredAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  orders.push(order);

  const orderWithDetails = {
    ...order,
    package: {
      ...package,
      coach: coaches.find(c => c.id === package.coachId)
    }
  };

  res.status(201).json({
    success: true,
    data: orderWithDetails,
    message: '订单创建成功'
  });
});

app.get('/api/orders/my', (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  let filteredOrders = orders;

  if (status) {
    filteredOrders = filteredOrders.filter(order => order.status === status);
  }

  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  const ordersWithDetails = paginatedOrders.map(order => ({
    ...order,
    package: {
      ...packages.find(pkg => pkg.id === order.packageId),
      coach: coaches.find(c => c.id === packages.find(pkg => pkg.id === order.packageId)?.coachId)
    }
  }));

  res.json({
    success: true,
    data: {
      orders: ordersWithDetails,
      pagination: {
        total: filteredOrders.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredOrders.length / limit)
      }
    }
  });
});

app.post('/api/orders/:id/pay', (req, res) => {
  const { id } = req.params;
  const order = orders.find(o => o.id === id);

  if (!order) {
    return res.status(404).json({
      success: false,
      error: '订单不存在',
      code: 'ORDER_NOT_FOUND'
    });
  }

  if (order.status !== 'pending') {
    return res.status(400).json({
      success: false,
      error: '订单状态不正确',
      code: 'INVALID_ORDER_STATUS'
    });
  }

  // 检查订单是否过期
  if (new Date() > new Date(order.expiredAt)) {
    order.status = 'cancelled';
    return res.status(400).json({
      success: false,
      error: '订单已过期',
      code: 'ORDER_EXPIRED'
    });
  }

  // 模拟支付成功
  order.status = 'paid';
  order.paymentId = `PAY_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  order.paidAt = new Date().toISOString();
  order.updatedAt = new Date().toISOString();

  res.json({
    success: true,
    data: order,
    message: '支付成功'
  });
});

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: 'Shuan-Q API Server (测试版)',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: [
        'POST /api/auth/send-code',
        'POST /api/auth/login'
      ],
      coaches: [
        'GET /api/coaches',
        'GET /api/coaches/:id'
      ],
      appointments: [
        'POST /api/appointments',
        'GET /api/appointments/my'
      ],
      packages: [
        'GET /api/packages',
        'GET /api/packages/:id'
      ],
      orders: [
        'POST /api/orders',
        'GET /api/orders/my',
        'POST /api/orders/:id/pay'
      ]
    }
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Shuan-Q 测试服务器启动成功！`);
  console.log(`📖 API地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`\n📱 测试提示: 验证码会在控制台显示`);
});
