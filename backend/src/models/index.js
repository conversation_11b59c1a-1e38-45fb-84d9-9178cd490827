const sequelize = require('../config/database');
const User = require('./User')(sequelize);
const Coach = require('./Coach')(sequelize);
const Appointment = require('./Appointment')(sequelize);
const CoursePackage = require('./CoursePackage')(sequelize);
const CourseCategory = require('./CourseCategory')(sequelize);
const Order = require('./Order')(sequelize);

// 定义模型关联
User.hasOne(Coach, {
  foreignKey: 'userId',
  as: 'coachProfile'
});

Coach.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

User.hasMany(Appointment, {
  foreignKey: 'studentId',
  as: 'studentAppointments'
});

Coach.hasMany(Appointment, {
  foreignKey: 'coachId',
  as: 'coachAppointments'
});

Appointment.belongsTo(User, {
  foreignKey: 'studentId',
  as: 'student'
});

Appointment.belongsTo(Coach, {
  foreignKey: 'coachId',
  as: 'coach'
});

// 新增模型关联
CoursePackage.belongsTo(Coach, {
  foreignKey: 'coachId',
  as: 'coach'
});

Coach.hasMany(CoursePackage, {
  foreignKey: 'coachId',
  as: 'packages'
});

Order.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

Order.belongsTo(CoursePackage, {
  foreignKey: 'packageId',
  as: 'package'
});

User.hasMany(Order, {
  foreignKey: 'userId',
  as: 'orders'
});

CoursePackage.hasMany(Order, {
  foreignKey: 'packageId',
  as: 'orders'
});

Appointment.belongsTo(CoursePackage, {
  foreignKey: 'packageId',
  as: 'package'
});

CoursePackage.hasMany(Appointment, {
  foreignKey: 'packageId',
  as: 'appointments'
});

module.exports = {
  sequelize,
  User,
  Coach,
  Appointment,
  CoursePackage,
  CourseCategory,
  Order
};
