const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        isNumeric: true,
        len: [11, 11]
      }
    },
    nickname: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    userType: {
      type: DataTypes.ENUM('student', 'coach'),
      allowNull: false,
      field: 'user_type'
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    gender: {
      type: DataTypes.ENUM('male', 'female'),
      allowNull: true
    },
    birthDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'birth_date'
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    underscored: true
  });

  User.associate = (models) => {
    // 用户可以是教练
    User.hasOne(models.Coach, {
      foreignKey: 'user_id',
      as: 'coachProfile'
    });
    
    // 用户可以有多个预约
    User.hasMany(models.Appointment, {
      foreignKey: 'student_id',
      as: 'appointments'
    });
    
    // 用户可以有多个订单
    User.hasMany(models.Order, {
      foreignKey: 'user_id',
      as: 'orders'
    });
  };

  return User;
};
