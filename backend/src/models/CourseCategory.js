const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CourseCategory = sequelize.define('CourseCategory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    icon: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'sort_order'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'course_categories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    underscored: true,
    indexes: [
      {
        fields: ['sort_order']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  CourseCategory.associate = (models) => {
    // 分类可以有多个课程包
    CourseCategory.hasMany(models.CoursePackage, {
      foreignKey: 'category',
      sourceKey: 'name',
      as: 'packages'
    });
  };

  return CourseCategory;
};
