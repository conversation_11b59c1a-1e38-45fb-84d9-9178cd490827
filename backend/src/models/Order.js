const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Order = sequelize.define('Order', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    orderNumber: {
      type: DataTypes.STRING(32),
      allowNull: false,
      unique: true,
      field: 'order_number'
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id'
    },
    packageId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'package_id'
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    originalAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'original_amount'
    },
    discountAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      field: 'discount_amount'
    },
    status: {
      type: DataTypes.ENUM('pending', 'paid', 'cancelled', 'refunded'),
      allowNull: false,
      defaultValue: 'pending'
    },
    paymentMethod: {
      type: DataTypes.ENUM('wechat', 'alipay', 'balance'),
      allowNull: true,
      field: 'payment_method'
    },
    paymentId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'payment_id'
    },
    paidAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'paid_at'
    },
    expiredAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'expired_at'
    },
    refundReason: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'refund_reason'
    },
    refundedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'refunded_at'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    underscored: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['package_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['order_number']
      },
      {
        fields: ['payment_id']
      }
    ]
  });

  Order.associate = (models) => {
    // 订单属于用户
    Order.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
    
    // 订单属于课程包
    Order.belongsTo(models.CoursePackage, {
      foreignKey: 'package_id',
      as: 'package'
    });
    
    // 订单可以有多个支付记录
    Order.hasMany(models.Payment, {
      foreignKey: 'order_id',
      as: 'payments'
    });
  };

  // 生成订单号
  Order.generateOrderNumber = () => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `SQ${timestamp}${random}`.toUpperCase();
  };

  return Order;
};
