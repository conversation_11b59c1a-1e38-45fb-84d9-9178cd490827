const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CoursePackage = sequelize.define('CoursePackage', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    coachId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'coach_id'
    },
    totalSessions: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      field: 'total_sessions'
    },
    usedSessions: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'used_sessions'
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    originalPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'original_price'
    },
    validityDays: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 30,
      field: 'validity_days'
    },
    level: {
      type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
      allowNull: false,
      defaultValue: 'beginner'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    features: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'course_packages',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    underscored: true,
    indexes: [
      {
        fields: ['coach_id']
      },
      {
        fields: ['category']
      },
      {
        fields: ['level']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  CoursePackage.associate = (models) => {
    // 课程包属于教练
    CoursePackage.belongsTo(models.Coach, {
      foreignKey: 'coach_id',
      as: 'coach'
    });
    
    // 课程包可以有多个订单
    CoursePackage.hasMany(models.Order, {
      foreignKey: 'package_id',
      as: 'orders'
    });
    
    // 课程包可以有多个预约
    CoursePackage.hasMany(models.Appointment, {
      foreignKey: 'package_id',
      as: 'appointments'
    });
  };

  return CoursePackage;
};
