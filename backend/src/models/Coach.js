const { DataTypes } = require("sequelize"); module.exports = (sequelize) => { const Coach = sequelize.define("Coach", { id: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV4, primaryKey: true }, userId: { type: DataTypes.UUID, allowNull: false, field: "user_id" }, description: { type: DataTypes.TEXT, allowNull: true }, experience: { type: DataTypes.TEXT, allowNull: true }, rating: { type: DataTypes.DECIMAL(3, 2), defaultValue: 0.00 }, ratingCount: { type: DataTypes.INTEGER, defaultValue: 0, field: "rating_count" }, specialties: { type: DataTypes.JSON, allowNull: true }, location: { type: DataTypes.STRING(100), allowNull: true }, hourlyRate: { type: DataTypes.DECIMAL(10, 2), allowNull: true, field: "hourly_rate" }, status: { type: DataTypes.ENUM("active", "inactive"), defaultValue: "active" } }, { tableName: "coaches", timestamps: true, createdAt: "created_at", updatedAt: "updated_at", underscored: true }); return Coach; };
