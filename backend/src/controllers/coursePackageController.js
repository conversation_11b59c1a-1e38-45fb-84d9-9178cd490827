const { CoursePackage, Coach, User } = require('../models');
const { Op } = require('sequelize');

// 获取课程包列表
const getPackages = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      level,
      coachId,
      minPrice,
      maxPrice,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { isActive: true };

    // 筛选条件
    if (category) where.category = category;
    if (level) where.level = level;
    if (coachId) where.coachId = coachId;
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price[Op.gte] = minPrice;
      if (maxPrice) where.price[Op.lte] = maxPrice;
    }

    const { count, rows } = await CoursePackage.findAndCountAll({
      where,
      include: [
        {
          model: Coach,
          as: 'coach',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname', 'avatar']
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    res.json({
      success: true,
      data: {
        packages: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取课程包列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取课程包列表失败',
      code: 'GET_PACKAGES_FAILED'
    });
  }
};

// 获取课程包详情
const getPackageById = async (req, res) => {
  try {
    const { id } = req.params;

    const package = await CoursePackage.findByPk(id, {
      include: [
        {
          model: Coach,
          as: 'coach',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname', 'avatar', 'phone']
            }
          ]
        }
      ]
    });

    if (!package) {
      return res.status(404).json({
        success: false,
        error: '课程包不存在',
        code: 'PACKAGE_NOT_FOUND'
      });
    }

    if (!package.isActive) {
      return res.status(404).json({
        success: false,
        error: '课程包已下架',
        code: 'PACKAGE_INACTIVE'
      });
    }

    res.json({
      success: true,
      data: package
    });
  } catch (error) {
    console.error('获取课程包详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取课程包详情失败',
      code: 'GET_PACKAGE_FAILED'
    });
  }
};

// 创建课程包 (教练专用)
const createPackage = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      name,
      description,
      totalSessions,
      price,
      originalPrice,
      validityDays,
      level,
      category,
      features
    } = req.body;

    // 验证教练身份
    const coach = await Coach.findOne({ where: { userId } });
    if (!coach) {
      return res.status(403).json({
        success: false,
        error: '只有教练可以创建课程包',
        code: 'COACH_REQUIRED'
      });
    }

    const package = await CoursePackage.create({
      name,
      description,
      coachId: coach.id,
      totalSessions,
      price,
      originalPrice: originalPrice || price,
      validityDays: validityDays || 30,
      level,
      category,
      features: features || []
    });

    res.status(201).json({
      success: true,
      data: package,
      message: '课程包创建成功'
    });
  } catch (error) {
    console.error('创建课程包失败:', error);
    res.status(500).json({
      success: false,
      error: '创建课程包失败',
      code: 'CREATE_PACKAGE_FAILED'
    });
  }
};

// 更新课程包 (教练专用)
const updatePackage = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const updateData = req.body;

    // 验证教练身份
    const coach = await Coach.findOne({ where: { userId } });
    if (!coach) {
      return res.status(403).json({
        success: false,
        error: '只有教练可以更新课程包',
        code: 'COACH_REQUIRED'
      });
    }

    const package = await CoursePackage.findOne({
      where: { id, coachId: coach.id }
    });

    if (!package) {
      return res.status(404).json({
        success: false,
        error: '课程包不存在或无权限',
        code: 'PACKAGE_NOT_FOUND'
      });
    }

    await package.update(updateData);

    res.json({
      success: true,
      data: package,
      message: '课程包更新成功'
    });
  } catch (error) {
    console.error('更新课程包失败:', error);
    res.status(500).json({
      success: false,
      error: '更新课程包失败',
      code: 'UPDATE_PACKAGE_FAILED'
    });
  }
};

// 获取我的课程包 (教练专用)
const getMyPackages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    // 验证教练身份
    const coach = await Coach.findOne({ where: { userId } });
    if (!coach) {
      return res.status(403).json({
        success: false,
        error: '只有教练可以查看自己的课程包',
        code: 'COACH_REQUIRED'
      });
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await CoursePackage.findAndCountAll({
      where: { coachId: coach.id },
      limit: parseInt(limit),
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        packages: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取我的课程包失败:', error);
    res.status(500).json({
      success: false,
      error: '获取我的课程包失败',
      code: 'GET_MY_PACKAGES_FAILED'
    });
  }
};

module.exports = {
  getPackages,
  getPackageById,
  createPackage,
  updatePackage,
  getMyPackages
};
