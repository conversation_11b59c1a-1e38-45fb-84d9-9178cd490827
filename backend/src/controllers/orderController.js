const { Order, CoursePackage, Coach, User } = require('../models');
const { Op } = require('sequelize');

// 创建订单
const createOrder = async (req, res) => {
  try {
    const userId = req.user.id;
    const { packageId, paymentMethod = 'wechat' } = req.body;

    // 验证课程包
    const package = await CoursePackage.findByPk(packageId, {
      include: [
        {
          model: Coach,
          as: 'coach',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['nickname']
            }
          ]
        }
      ]
    });

    if (!package) {
      return res.status(404).json({
        success: false,
        error: '课程包不存在',
        code: 'PACKAGE_NOT_FOUND'
      });
    }

    if (!package.isActive) {
      return res.status(400).json({
        success: false,
        error: '课程包已下架',
        code: 'PACKAGE_INACTIVE'
      });
    }

    // 生成订单
    const orderNumber = Order.generateOrderNumber();
    const expiredAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期

    const order = await Order.create({
      orderNumber,
      userId,
      packageId,
      amount: package.price,
      originalAmount: package.originalPrice,
      discountAmount: package.originalPrice - package.price,
      paymentMethod,
      expiredAt
    });

    // 返回订单信息
    const orderWithDetails = await Order.findByPk(order.id, {
      include: [
        {
          model: CoursePackage,
          as: 'package',
          include: [
            {
              model: Coach,
              as: 'coach',
              include: [
                {
                  model: User,
                  as: 'user',
                  attributes: ['nickname']
                }
              ]
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: orderWithDetails,
      message: '订单创建成功'
    });
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      success: false,
      error: '创建订单失败',
      code: 'CREATE_ORDER_FAILED'
    });
  }
};

// 获取我的订单
const getMyOrders = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      status
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { userId };

    if (status) {
      where.status = status;
    }

    const { count, rows } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: CoursePackage,
          as: 'package',
          include: [
            {
              model: Coach,
              as: 'coach',
              include: [
                {
                  model: User,
                  as: 'user',
                  attributes: ['nickname', 'avatar']
                }
              ]
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        orders: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取我的订单失败:', error);
    res.status(500).json({
      success: false,
      error: '获取我的订单失败',
      code: 'GET_MY_ORDERS_FAILED'
    });
  }
};

// 获取订单详情
const getOrderById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId },
      include: [
        {
          model: CoursePackage,
          as: 'package',
          include: [
            {
              model: Coach,
              as: 'coach',
              include: [
                {
                  model: User,
                  as: 'user',
                  attributes: ['nickname', 'avatar', 'phone']
                }
              ]
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        error: '订单不存在',
        code: 'ORDER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取订单详情失败',
      code: 'GET_ORDER_FAILED'
    });
  }
};

// 支付订单 (模拟支付)
const payOrder = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        error: '订单不存在',
        code: 'ORDER_NOT_FOUND'
      });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: '订单状态不正确',
        code: 'INVALID_ORDER_STATUS'
      });
    }

    // 检查订单是否过期
    if (order.expiredAt && new Date() > order.expiredAt) {
      await order.update({ status: 'cancelled' });
      return res.status(400).json({
        success: false,
        error: '订单已过期',
        code: 'ORDER_EXPIRED'
      });
    }

    // 模拟支付成功
    const paymentId = `PAY_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    await order.update({
      status: 'paid',
      paymentId,
      paidAt: new Date()
    });

    res.json({
      success: true,
      data: order,
      message: '支付成功'
    });
  } catch (error) {
    console.error('支付订单失败:', error);
    res.status(500).json({
      success: false,
      error: '支付订单失败',
      code: 'PAY_ORDER_FAILED'
    });
  }
};

// 取消订单
const cancelOrder = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        error: '订单不存在',
        code: 'ORDER_NOT_FOUND'
      });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: '只能取消待支付的订单',
        code: 'INVALID_ORDER_STATUS'
      });
    }

    await order.update({ status: 'cancelled' });

    res.json({
      success: true,
      data: order,
      message: '订单取消成功'
    });
  } catch (error) {
    console.error('取消订单失败:', error);
    res.status(500).json({
      success: false,
      error: '取消订单失败',
      code: 'CANCEL_ORDER_FAILED'
    });
  }
};

module.exports = {
  createOrder,
  getMyOrders,
  getOrderById,
  payOrder,
  cancelOrder
};
