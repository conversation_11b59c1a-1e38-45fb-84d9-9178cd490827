const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { sequelize } = require('./models');
const { initCourseCategories } = require('./utils/seedData');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 路由
const authRoutes = require('./routes/auth');
const coachRoutes = require('./routes/coaches');
const appointmentRoutes = require('./routes/appointments');
const packageRoutes = require('./routes/packages');
const orderRoutes = require('./routes/orders');

app.use('/api/auth', authRoutes);
app.use('/api/coaches', coachRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/packages', packageRoutes);
app.use('/api/orders', orderRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: 'Shuan-Q API Server',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: [
        'POST /api/auth/send-code',
        'POST /api/auth/login',
        'GET /api/auth/me',
        'PUT /api/auth/profile'
      ],
      coaches: [
        'GET /api/coaches',
        'GET /api/coaches/:id',
        'POST /api/coaches/profile',
        'PUT /api/coaches/profile'
      ],
      appointments: [
        'POST /api/appointments',
        'GET /api/appointments/my',
        'GET /api/appointments/:id',
        'PUT /api/appointments/:id/status'
      ],
      packages: [
        'GET /api/packages',
        'GET /api/packages/:id',
        'POST /api/packages',
        'PUT /api/packages/:id',
        'GET /api/packages/my/list'
      ],
      orders: [
        'POST /api/orders',
        'GET /api/orders/my',
        'GET /api/orders/:id',
        'POST /api/orders/:id/pay',
        'POST /api/orders/:id/cancel'
      ]
    }
  });
});

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    await sequelize.authenticate();
    res.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    code: 'ENDPOINT_NOT_FOUND',
    path: req.originalUrl
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: '服务器内部错误',
    code: 'INTERNAL_SERVER_ERROR',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// 数据库连接和服务器启动
const startServer = async () => {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 同步数据库模型 (开发环境)
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步完成');

      // 初始化种子数据
      await initCourseCategories();
    }

    // 启动服务器
    app.listen(PORT, () => {
      console.log(`🚀 Shuan-Q API Server running on port ${PORT}`);
      console.log(`📖 API Documentation: http://localhost:${PORT}`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

startServer();
