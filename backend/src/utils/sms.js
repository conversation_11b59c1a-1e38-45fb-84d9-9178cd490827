// 模拟短信验证码服务
// 在实际项目中，这里应该集成真实的短信服务商API

const verificationCodes = new Map();

const generateCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

const sendVerificationCode = async (phone) => {
  try {
    const code = generateCode();
    
    // 存储验证码，5分钟过期
    verificationCodes.set(phone, {
      code,
      expiresAt: Date.now() + 5 * 60 * 1000
    });
    
    // 模拟发送短信
    console.log(`发送验证码到 ${phone}: ${code}`);
    
    // 在实际项目中，这里应该调用短信服务商API
    // 例如：阿里云短信、腾讯云短信等
    
    return {
      success: true,
      message: '验证码发送成功'
    };
  } catch (error) {
    console.error('发送验证码失败:', error);
    return {
      success: false,
      message: '验证码发送失败'
    };
  }
};

const verifyCode = (phone, code) => {
  const stored = verificationCodes.get(phone);
  
  if (!stored) {
    return {
      success: false,
      message: '验证码不存在或已过期'
    };
  }
  
  if (Date.now() > stored.expiresAt) {
    verificationCodes.delete(phone);
    return {
      success: false,
      message: '验证码已过期'
    };
  }
  
  if (stored.code !== code) {
    return {
      success: false,
      message: '验证码错误'
    };
  }
  
  // 验证成功后删除验证码
  verificationCodes.delete(phone);
  
  return {
    success: true,
    message: '验证码验证成功'
  };
};

module.exports = {
  sendVerificationCode,
  verifyCode
};
