const { CourseCategory } = require('../models');

// 初始化课程分类数据
const initCourseCategories = async () => {
  try {
    const categories = [
      {
        name: '基础入门',
        description: '适合台球初学者的基础课程',
        icon: '🎱',
        sortOrder: 1
      },
      {
        name: '技巧提升',
        description: '提升台球技巧和战术的进阶课程',
        icon: '🎯',
        sortOrder: 2
      },
      {
        name: '专业训练',
        description: '专业级别的台球训练课程',
        icon: '🏆',
        sortOrder: 3
      },
      {
        name: '比赛指导',
        description: '比赛技巧和心理素质训练',
        icon: '🥇',
        sortOrder: 4
      },
      {
        name: '私人定制',
        description: '根据个人需求定制的专属课程',
        icon: '⭐',
        sortOrder: 5
      }
    ];

    for (const categoryData of categories) {
      const [category, created] = await CourseCategory.findOrCreate({
        where: { name: categoryData.name },
        defaults: categoryData
      });

      if (created) {
        console.log(`✅ 创建课程分类: ${category.name}`);
      } else {
        console.log(`📋 课程分类已存在: ${category.name}`);
      }
    }

    console.log('🎉 课程分类初始化完成');
  } catch (error) {
    console.error('❌ 课程分类初始化失败:', error);
  }
};

module.exports = {
  initCourseCategories
};
