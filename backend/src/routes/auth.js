const express = require('express');
const { User } = require('../models');
const { generateToken } = require('../utils/jwt');
const { sendVerificationCode, verifyCode } = require('../utils/sms');
const { validateLogin, validateSendCode } = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 发送验证码
router.post('/send-code', validateSendCode, async (req, res) => {
  try {
    const { phone } = req.body;
    
    const result = await sendVerificationCode(phone);
    
    if (result.success) {
      res.json({
        success: true,
        message: '验证码已发送'
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.message,
        code: 'SMS_SEND_FAILED'
      });
    }
  } catch (error) {
    console.error('发送验证码错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    });
  }
});

// 验证码登录
router.post('/login', validateLogin, async (req, res) => {
  try {
    const { phone, code } = req.body;
    
    // 验证验证码
    const codeVerification = verifyCode(phone, code);
    if (!codeVerification.success) {
      return res.status(400).json({
        success: false,
        error: codeVerification.message,
        code: 'INVALID_CODE'
      });
    }
    
    // 查找或创建用户
    let user = await User.findOne({ where: { phone } });
    
    if (!user) {
      // 新用户注册
      user = await User.create({
        phone,
        nickname: `用户${phone.slice(-4)}`,
        userType: 'student' // 默认为学员
      });
    }
    
    // 生成JWT token
    const token = generateToken(user.id, user.userType);
    
    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          userType: user.userType
        }
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      error: '登录失败',
      code: 'LOGIN_FAILED'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    
    res.json({
      success: true,
      data: {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        avatar: user.avatar,
        userType: user.userType,
        email: user.email,
        gender: user.gender,
        location: user.location
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      error: '获取用户信息失败',
      code: 'GET_USER_FAILED'
    });
  }
});

module.exports = router;
