const express = require('express');
const { Op } = require('sequelize');
const { User, Coach, Appointment } = require('../models');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateCreateAppointment } = require('../middleware/validation');

const router = express.Router();

// 创建预约
router.post('/', authenticateToken, requireRole(['student']), validateCreateAppointment, async (req, res) => {
  try {
    const { coachId, packageId, date, startTime, endTime, notes } = req.body;
    const studentId = req.user.id;

    // 检查教练是否存在
    const coach = await Coach.findByPk(coachId);
    if (!coach) {
      return res.status(404).json({
        success: false,
        error: '教练不存在',
        code: 'COACH_NOT_FOUND'
      });
    }

    // 检查时间冲突
    const conflictingAppointment = await Appointment.findOne({
      where: {
        coachId,
        appointmentDate: date,
        status: { [Op.in]: ['pending', 'confirmed'] },
        [Op.or]: [
          {
            startTime: { [Op.between]: [startTime, endTime] }
          },
          {
            endTime: { [Op.between]: [startTime, endTime] }
          },
          {
            [Op.and]: [
              { startTime: { [Op.lte]: startTime } },
              { endTime: { [Op.gte]: endTime } }
            ]
          }
        ]
      }
    });

    if (conflictingAppointment) {
      return res.status(400).json({
        success: false,
        error: '该时间段已被预约',
        code: 'TIME_CONFLICT'
      });
    }

    // 创建预约
    const appointment = await Appointment.create({
      studentId,
      coachId,
      packageId,
      appointmentDate: date,
      startTime,
      endTime,
      notes,
      status: 'pending'
    });

    res.json({
      success: true,
      data: {
        id: appointment.id,
        studentId: appointment.studentId,
        coachId: appointment.coachId,
        packageId: appointment.packageId,
        date: appointment.appointmentDate,
        startTime: appointment.startTime,
        endTime: appointment.endTime,
        status: appointment.status,
        notes: appointment.notes
      }
    });
  } catch (error) {
    console.error('创建预约错误:', error);
    res.status(500).json({
      success: false,
      error: '创建预约失败',
      code: 'CREATE_APPOINTMENT_FAILED'
    });
  }
});

// 获取我的预约 (学员)
router.get('/my', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, page = 1, limit = 10 } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (req.user.userType === 'student') {
      whereClause.studentId = userId;
    } else if (req.user.userType === 'coach') {
      // 如果是教练，查找教练ID
      const coach = await Coach.findOne({ where: { userId } });
      if (coach) {
        whereClause.coachId = coach.id;
      }
    }

    if (status) {
      whereClause.status = status;
    }

    const { count, rows: appointments } = await Appointment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'nickname', 'avatar', 'phone']
        },
        {
          model: Coach,
          as: 'coach',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'avatar', 'phone']
          }]
        }
      ],
      order: [['appointmentDate', 'DESC'], ['startTime', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const formattedAppointments = appointments.map(appointment => ({
      id: appointment.id,
      date: appointment.appointmentDate,
      startTime: appointment.startTime,
      endTime: appointment.endTime,
      status: appointment.status,
      notes: appointment.notes,
      student: appointment.student ? {
        id: appointment.student.id,
        name: appointment.student.nickname,
        avatar: appointment.student.avatar,
        phone: appointment.student.phone
      } : null,
      coach: appointment.coach ? {
        id: appointment.coach.id,
        name: appointment.coach.user.nickname,
        avatar: appointment.coach.user.avatar,
        phone: appointment.coach.user.phone
      } : null,
      createdAt: appointment.createdAt
    }));

    res.json({
      success: true,
      data: {
        appointments: formattedAppointments,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取预约列表错误:', error);
    res.status(500).json({
      success: false,
      error: '获取预约列表失败',
      code: 'GET_APPOINTMENTS_FAILED'
    });
  }
});

// 更新预约状态
router.put('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const user = req.user;

    const appointment = await Appointment.findByPk(id, {
      include: [
        {
          model: Coach,
          as: 'coach',
          include: [{
            model: User,
            as: 'user'
          }]
        }
      ]
    });

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: '预约不存在',
        code: 'APPOINTMENT_NOT_FOUND'
      });
    }

    // 权限检查
    const isStudent = appointment.studentId === user.id;
    const isCoach = appointment.coach && appointment.coach.user.id === user.id;

    if (!isStudent && !isCoach) {
      return res.status(403).json({
        success: false,
        error: '无权限操作此预约',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // 状态转换规则
    const allowedTransitions = {
      'pending': ['confirmed', 'cancelled'],
      'confirmed': ['completed', 'cancelled'],
      'completed': [],
      'cancelled': []
    };

    if (!allowedTransitions[appointment.status].includes(status)) {
      return res.status(400).json({
        success: false,
        error: '无效的状态转换',
        code: 'INVALID_STATUS_TRANSITION'
      });
    }

    await appointment.update({ status });

    res.json({
      success: true,
      data: {
        id: appointment.id,
        status: appointment.status
      }
    });
  } catch (error) {
    console.error('更新预约状态错误:', error);
    res.status(500).json({
      success: false,
      error: '更新预约状态失败',
      code: 'UPDATE_APPOINTMENT_FAILED'
    });
  }
});

// 获取预约详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const appointment = await Appointment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'nickname', 'avatar', 'phone']
        },
        {
          model: Coach,
          as: 'coach',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'avatar', 'phone']
          }]
        }
      ]
    });

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: '预约不存在',
        code: 'APPOINTMENT_NOT_FOUND'
      });
    }

    // 权限检查
    const isStudent = appointment.studentId === user.id;
    const isCoach = appointment.coach && appointment.coach.user.id === user.id;

    if (!isStudent && !isCoach) {
      return res.status(403).json({
        success: false,
        error: '无权限查看此预约',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    res.json({
      success: true,
      data: {
        id: appointment.id,
        date: appointment.appointmentDate,
        startTime: appointment.startTime,
        endTime: appointment.endTime,
        status: appointment.status,
        notes: appointment.notes,
        student: {
          id: appointment.student.id,
          name: appointment.student.nickname,
          avatar: appointment.student.avatar,
          phone: appointment.student.phone
        },
        coach: {
          id: appointment.coach.id,
          name: appointment.coach.user.nickname,
          avatar: appointment.coach.user.avatar,
          phone: appointment.coach.user.phone
        },
        createdAt: appointment.createdAt,
        updatedAt: appointment.updatedAt
      }
    });
  } catch (error) {
    console.error('获取预约详情错误:', error);
    res.status(500).json({
      success: false,
      error: '获取预约详情失败',
      code: 'GET_APPOINTMENT_FAILED'
    });
  }
});

module.exports = router;
