const express = require('express');
const { Op } = require('sequelize');
const { User, Coach } = require('../models');
const { authenticateToken, requireRole } = require('../middleware/auth');

const router = express.Router();

// 获取教练列表
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      location,
      specialty,
      minRating,
      sortBy = 'rating',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { status: 'active' };

    // 地区筛选
    if (location) {
      whereClause.location = { [Op.like]: `%${location}%` };
    }

    // 评分筛选
    if (minRating) {
      whereClause.rating = { [Op.gte]: parseFloat(minRating) };
    }

    // 专长筛选
    if (specialty) {
      whereClause.specialties = { [Op.like]: `%${specialty}%` };
    }

    const { count, rows: coaches } = await Coach.findAndCountAll({
      where: whereClause,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'nickname', 'avatar', 'phone']
      }],
      order: [[sortBy, sortOrder.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const formattedCoaches = coaches.map(coach => ({
      id: coach.id,
      name: coach.user.nickname,
      avatar: coach.user.avatar,
      description: coach.description,
      experience: coach.experience,
      rating: parseFloat(coach.rating),
      ratingCount: coach.ratingCount,
      specialties: coach.specialties || [],
      location: coach.location,
      hourlyRate: parseFloat(coach.hourlyRate || 0),
      status: coach.status
    }));

    res.json({
      success: true,
      data: {
        coaches: formattedCoaches,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取教练列表错误:', error);
    res.status(500).json({
      success: false,
      error: '获取教练列表失败',
      code: 'GET_COACHES_FAILED'
    });
  }
});

// 获取教练详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const coach = await Coach.findByPk(id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'nickname', 'avatar', 'phone', 'email']
      }]
    });

    if (!coach) {
      return res.status(404).json({
        success: false,
        error: '教练不存在',
        code: 'COACH_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      data: {
        id: coach.id,
        name: coach.user.nickname,
        avatar: coach.user.avatar,
        description: coach.description,
        experience: coach.experience,
        rating: parseFloat(coach.rating),
        ratingCount: coach.ratingCount,
        specialties: coach.specialties || [],
        location: coach.location,
        hourlyRate: parseFloat(coach.hourlyRate || 0),
        status: coach.status,
        contact: {
          phone: coach.user.phone,
          email: coach.user.email
        }
      }
    });
  } catch (error) {
    console.error('获取教练详情错误:', error);
    res.status(500).json({
      success: false,
      error: '获取教练详情失败',
      code: 'GET_COACH_FAILED'
    });
  }
});

// 创建教练资料 (需要认证)
router.post('/profile', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    
    // 检查是否已经是教练
    const existingCoach = await Coach.findOne({ where: { userId: user.id } });
    if (existingCoach) {
      return res.status(400).json({
        success: false,
        error: '您已经是教练了',
        code: 'ALREADY_COACH'
      });
    }

    const {
      description,
      experience,
      specialties,
      location,
      hourlyRate
    } = req.body;

    // 创建教练资料
    const coach = await Coach.create({
      userId: user.id,
      description,
      experience,
      specialties,
      location,
      hourlyRate
    });

    // 更新用户类型为教练
    await user.update({ userType: 'coach' });

    res.json({
      success: true,
      data: {
        id: coach.id,
        description: coach.description,
        experience: coach.experience,
        specialties: coach.specialties,
        location: coach.location,
        hourlyRate: parseFloat(coach.hourlyRate || 0)
      }
    });
  } catch (error) {
    console.error('创建教练资料错误:', error);
    res.status(500).json({
      success: false,
      error: '创建教练资料失败',
      code: 'CREATE_COACH_FAILED'
    });
  }
});

// 更新教练资料 (需要教练权限)
router.put('/profile', authenticateToken, requireRole(['coach']), async (req, res) => {
  try {
    const user = req.user;
    
    const coach = await Coach.findOne({ where: { userId: user.id } });
    if (!coach) {
      return res.status(404).json({
        success: false,
        error: '教练资料不存在',
        code: 'COACH_PROFILE_NOT_FOUND'
      });
    }

    const {
      description,
      experience,
      specialties,
      location,
      hourlyRate
    } = req.body;

    await coach.update({
      description: description || coach.description,
      experience: experience || coach.experience,
      specialties: specialties || coach.specialties,
      location: location || coach.location,
      hourlyRate: hourlyRate || coach.hourlyRate
    });

    res.json({
      success: true,
      data: {
        id: coach.id,
        description: coach.description,
        experience: coach.experience,
        specialties: coach.specialties,
        location: coach.location,
        hourlyRate: parseFloat(coach.hourlyRate || 0)
      }
    });
  } catch (error) {
    console.error('更新教练资料错误:', error);
    res.status(500).json({
      success: false,
      error: '更新教练资料失败',
      code: 'UPDATE_COACH_FAILED'
    });
  }
});

module.exports = router;
