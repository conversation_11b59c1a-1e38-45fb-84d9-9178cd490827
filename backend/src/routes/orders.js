const express = require('express');
const router = express.Router();
const {
  createOrder,
  getMyOrders,
  getOrderById,
  payOrder,
  cancelOrder
} = require('../controllers/orderController');
const authMiddleware = require('../middleware/auth');
const { body, param, query } = require('express-validator');
const validate = require('../middleware/validate');

// 验证规则
const createOrderValidation = [
  body('packageId')
    .isUUID()
    .withMessage('课程包ID格式不正确'),
  body('paymentMethod')
    .optional()
    .isIn(['wechat', 'alipay', 'balance'])
    .withMessage('支付方式必须是 wechat、alipay 或 balance')
];

const orderIdValidation = [
  param('id')
    .isUUID()
    .withMessage('订单ID格式不正确')
];

const getOrdersValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('status')
    .optional()
    .isIn(['pending', 'paid', 'cancelled', 'refunded'])
    .withMessage('状态必须是 pending、paid、cancelled 或 refunded')
];

// 所有路由都需要认证
router.use(authMiddleware);

// 订单相关路由
router.post('/', createOrderValidation, validate, createOrder);
router.get('/my', getOrdersValidation, validate, getMyOrders);
router.get('/:id', orderIdValidation, validate, getOrderById);
router.post('/:id/pay', orderIdValidation, validate, payOrder);
router.post('/:id/cancel', orderIdValidation, validate, cancelOrder);

module.exports = router;
