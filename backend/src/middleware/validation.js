const { body, validationResult } = require('express-validator');

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: '参数验证失败',
      code: 'VALIDATION_ERROR',
      details: errors.array()
    });
  }
  next();
};

const validateLogin = [
  body('phone')
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号'),
  body('code')
    .isLength({ min: 4, max: 6 })
    .withMessage('验证码长度应为4-6位'),
  handleValidationErrors
];

const validateSendCode = [
  body('phone')
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号'),
  handleValidationErrors
];

const validateCreateAppointment = [
  body('coachId')
    .isUUID()
    .withMessage('教练ID格式错误'),
  body('packageId')
    .optional()
    .isUUID()
    .withMessage('课程包ID格式错误'),
  body('date')
    .isISO8601()
    .withMessage('日期格式错误'),
  body('startTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('开始时间格式错误'),
  body('endTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('结束时间格式错误'),
  handleValidationErrors
];

module.exports = {
  validateLogin,
  validateSendCode,
  validateCreateAppointment,
  handleValidationErrors
};
