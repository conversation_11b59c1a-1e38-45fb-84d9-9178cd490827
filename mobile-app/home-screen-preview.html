<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQ台球教學 - 首頁預覽</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.5);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 18px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 70px;
        }

        /* Banner輪播圖 */
        .banner-container {
            height: 280px;
            position: relative;
            overflow: hidden;
        }

        .banner-wrapper {
            display: flex;
            height: 100%;
            transition: transform 0.5s ease;
        }

        .banner-item {
            min-width: 100%;
            height: 100%;
            position: relative;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .banner-item:nth-child(1) {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
        }

        .banner-item:nth-child(2) {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }

        .banner-item:nth-child(3) {
            background: linear-gradient(45deg, #2980b9, #3498db);
        }

        .banner-item:nth-child(4) {
            background: linear-gradient(45deg, #8e44ad, #9b59b6);
        }

        .banner-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            padding: 20px;
        }

        .banner-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        }

        .banner-dots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .dot.active {
            background: white;
            width: 24px;
        }

        /* 搜索組件 - 延用商城樣式 */
        .search-container {
            padding: 16px;
            background: #f8f9fa;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            padding: 12px 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .search-icon {
            color: #999;
            margin-right: 12px;
            font-size: 20px;
        }

        .search-placeholder {
            color: #999;
            font-size: 16px;
            flex: 1;
        }

        /* 底部標籤欄 */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid #e1e1e1;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
        }

        .tab-item:hover {
            background: rgba(24, 144, 255, 0.1);
            transform: scale(1.05);
        }

        .tab-item.active {
            color: #1890ff;
            background: rgba(24, 144, 255, 0.1);
        }

        .tab-item.active .tab-icon {
            transform: scale(1.2);
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .page-label {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <div class="page-label">🏠 首頁</div>
            
            <div class="content">
                <!-- Banner輪播圖 -->
                <div class="banner-container">
                    <div class="banner-wrapper" id="bannerWrapper">
                        <div class="banner-item">
                            <div class="banner-overlay">
                                <div class="banner-title">WORLD WOMEN'S SNOOKER<br>CHAMPIONSHIP 2024</div>
                            </div>
                        </div>
                        <div class="banner-item">
                            <div class="banner-overlay">
                                <div class="banner-title">專業台球教學培訓</div>
                            </div>
                        </div>
                        <div class="banner-item">
                            <div class="banner-overlay">
                                <div class="banner-title">一對一專業指導</div>
                            </div>
                        </div>
                        <div class="banner-item">
                            <div class="banner-overlay">
                                <div class="banner-title">團體課程培訓</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 輪播圖指示點 -->
                    <div class="banner-dots">
                        <div class="dot active"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>

                <!-- 搜索組件 - 延用商城樣式 -->
                <div class="search-container">
                    <div class="search-bar">
                        <span class="search-icon">🔍</span>
                        <span class="search-placeholder">搜索商品、教練</span>
                    </div>
                </div>
            </div>
            
            <!-- 底部標籤欄 -->
            <div class="tab-bar">
                <div class="tab-item active" onclick="switchTab('home', this)">
                    <div class="tab-icon">🏠</div>
                    <div>首頁</div>
                </div>
                <div class="tab-item" onclick="switchTab('mall', this)">
                    <div class="tab-icon">🛍️</div>
                    <div>商城</div>
                </div>
                <div class="tab-item" onclick="switchTab('coach', this)">
                    <div class="tab-icon">👨‍🏫</div>
                    <div>教練</div>
                </div>
                <div class="tab-item" onclick="switchTab('profile', this)">
                    <div class="tab-icon">👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自動輪播功能
        let currentIndex = 0;
        const banners = document.querySelectorAll('.banner-item');
        const dots = document.querySelectorAll('.dot');
        const bannerWrapper = document.getElementById('bannerWrapper');
        const totalBanners = banners.length;

        function updateBanner() {
            // 更新輪播圖位置
            bannerWrapper.style.transform = `translateX(-${currentIndex * 100}%)`;
            
            // 更新指示點
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentIndex);
            });
        }

        function nextBanner() {
            currentIndex = (currentIndex + 1) % totalBanners;
            updateBanner();
        }

        // 4秒自動切換
        setInterval(nextBanner, 4000);

        // 點擊指示點切換
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentIndex = index;
                updateBanner();
            });
        });

        // Tab切換功能
        function switchTab(tabName, element) {
            // 移除所有active狀態
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加當前active狀態
            element.classList.add('active');
            
            // 根據不同Tab顯示對應頁面內容
            showTabContent(tabName);
        }

        function showTabContent(tabName) {
            const content = document.querySelector('.content');
            const pageLabel = document.querySelector('.page-label');
            
            switch(tabName) {
                case 'home':
                    pageLabel.innerHTML = '🏠 首頁';
                    content.innerHTML = `
                        <!-- Banner輪播圖 -->
                        <div class="banner-container">
                            <div class="banner-wrapper" id="bannerWrapper">
                                <div class="banner-item">
                                    <div class="banner-overlay">
                                        <div class="banner-title">WORLD WOMEN'S SNOOKER<br>CHAMPIONSHIP 2024</div>
                                    </div>
                                </div>
                                <div class="banner-item">
                                    <div class="banner-overlay">
                                        <div class="banner-title">專業台球教學培訓</div>
                                    </div>
                                </div>
                                <div class="banner-item">
                                    <div class="banner-overlay">
                                        <div class="banner-title">一對一專業指導</div>
                                    </div>
                                </div>
                                <div class="banner-item">
                                    <div class="banner-overlay">
                                        <div class="banner-title">團體課程培訓</div>
                                    </div>
                                </div>
                            </div>
                            <div class="banner-dots">
                                <div class="dot active"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>

                        <!-- 搜索組件 -->
                        <div class="search-container">
                            <div class="search-bar">
                                <span class="search-icon">🔍</span>
                                <span class="search-placeholder">搜索商品、教練</span>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'mall':
                    pageLabel.innerHTML = '🛍️ 商城';
                    content.innerHTML = `
                        <div style="padding: 20px; text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 20px;">🛍️</div>
                            <h2 style="color: #333; margin-bottom: 10px;">商城頁面</h2>
                            <p style="color: #666; margin-bottom: 20px;">這裡顯示商品列表和購物功能</p>
                            <div style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                                <h3 style="color: #1890ff; margin-bottom: 15px;">🔍 搜索商品</h3>
                                <div style="background: white; border-radius: 25px; padding: 12px 16px; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <span style="margin-right: 12px;">🔍</span>
                                    <span style="color: #999;">搜索商品、教練</span>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top: 20px;">
                                <div style="background: white; border-radius: 12px; padding: 15px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="font-size: 24px; margin-bottom: 8px;">🏀</div>
                                    <div style="font-size: 14px; color: #333;">台球桌</div>
                                    <div style="font-size: 12px; color: #1890ff; font-weight: bold;">¥888</div>
                                </div>
                                <div style="background: white; border-radius: 12px; padding: 15px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="font-size: 24px; margin-bottom: 8px;">🎯</div>
                                    <div style="font-size: 14px; color: #333;">球杆</div>
                                    <div style="font-size: 12px; color: #1890ff; font-weight: bold;">¥299</div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'coach':
                    pageLabel.innerHTML = '👨‍🏫 教練';
                    content.innerHTML = `
                        <div style="padding: 20px; text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 20px;">👨‍🏫</div>
                            <h2 style="color: #333; margin-bottom: 10px;">教練列表</h2>
                            <p style="color: #666; margin-bottom: 20px;">專業台球教練，一對一指導</p>
                            <div style="background: #f8f9fa; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                                <h3 style="color: #52c41a; margin-bottom: 15px;">🔍 搜索教練</h3>
                                <div style="background: white; border-radius: 25px; padding: 12px 16px; display: flex; align-items: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <span style="margin-right: 12px;">🔍</span>
                                    <span style="color: #999;">搜索教練</span>
                                </div>
                            </div>
                            <div style="text-align: left;">
                                <div style="background: white; border-radius: 12px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div style="width: 50px; height: 50px; border-radius: 25px; background: linear-gradient(135deg, #1890ff, #52c41a); display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                            <span style="color: white; font-size: 18px;">👨</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333; margin-bottom: 4px;">張偉教練</div>
                                            <div style="font-size: 12px; color: #666;">8年教學經驗 • ⭐ 4.9分</div>
                                        </div>
                                        <div style="background: #fadb14; color: white; padding: 4px 8px; border-radius: 4px; font-size: 10px;">金牌</div>
                                    </div>
                                    <div style="font-size: 14px; color: #333; margin-bottom: 8px;">擅長：基礎教學、技巧提升、比賽指導</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #1890ff; font-weight: bold;">¥200/小時</span>
                                        <button style="background: #1890ff; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px;">預約</button>
                                    </div>
                                </div>
                                <div style="background: white; border-radius: 12px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div style="width: 50px; height: 50px; border-radius: 25px; background: linear-gradient(135deg, #52c41a, #722ed1); display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                            <span style="color: white; font-size: 18px;">👩</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333; margin-bottom: 4px;">王芳教練</div>
                                            <div style="font-size: 12px; color: #666;">4年教學經驗 • ⭐ 4.7分</div>
                                        </div>
                                        <div style="background: #52c41a; color: white; padding: 4px 8px; border-radius: 4px; font-size: 10px;">高級</div>
                                    </div>
                                    <div style="font-size: 14px; color: #333; margin-bottom: 8px;">擅長：女性教學、基礎入門</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #1890ff; font-weight: bold;">¥150/小時</span>
                                        <button style="background: #52c41a; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px;">預約</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'profile':
                    pageLabel.innerHTML = '👤 我的';
                    content.innerHTML = `
                        <div style="padding: 20px;">
                            <div style="text-align: center; margin-bottom: 30px;">
                                <div style="width: 80px; height: 80px; border-radius: 40px; background: linear-gradient(135deg, #1890ff, #722ed1); display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                    <span style="color: white; font-size: 32px;">👤</span>
                                </div>
                                <h2 style="color: #333; margin-bottom: 5px;">張小明</h2>
                                <p style="color: #666; font-size: 14px;">VIP會員 • 138****8888</p>
                            </div>
                            
                            <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; text-align: center;">
                                    <div>
                                        <div style="color: #1890ff; font-size: 20px; font-weight: bold; margin-bottom: 5px;">¥1680.50</div>
                                        <div style="color: #666; font-size: 12px;">賬戶餘額</div>
                                    </div>
                                    <div>
                                        <div style="color: #52c41a; font-size: 20px; font-weight: bold; margin-bottom: 5px;">2580</div>
                                        <div style="color: #666; font-size: 12px;">積分</div>
                                    </div>
                                    <div>
                                        <div style="color: #fa8c16; font-size: 20px; font-weight: bold; margin-bottom: 5px;">365</div>
                                        <div style="color: #666; font-size: 12px;">會員天數</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">我的服務</h3>
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center;">
                                    <div style="cursor: pointer;">
                                        <div style="color: #1890ff; font-size: 24px; margin-bottom: 5px;">📅</div>
                                        <div style="font-size: 12px; color: #333;">我的預約</div>
                                        <div style="background: #ff4757; color: white; border-radius: 10px; font-size: 10px; padding: 2px 6px; margin-top: 2px; display: inline-block;">12</div>
                                    </div>
                                    <div style="cursor: pointer;">
                                        <div style="color: #52c41a; font-size: 24px; margin-bottom: 5px;">🎓</div>
                                        <div style="font-size: 12px; color: #333;">學習課程</div>
                                        <div style="background: #52c41a; color: white; border-radius: 10px; font-size: 10px; padding: 2px 6px; margin-top: 2px; display: inline-block;">8</div>
                                    </div>
                                    <div style="cursor: pointer;">
                                        <div style="color: #fa8c16; font-size: 24px; margin-bottom: 5px;">❤️</div>
                                        <div style="font-size: 12px; color: #333;">我的收藏</div>
                                        <div style="background: #fa8c16; color: white; border-radius: 10px; font-size: 10px; padding: 2px 6px; margin-top: 2px; display: inline-block;">25</div>
                                    </div>
                                    <div style="cursor: pointer;">
                                        <div style="color: #722ed1; font-size: 24px; margin-bottom: 5px;">⭐</div>
                                        <div style="font-size: 12px; color: #333;">我的評價</div>
                                        <div style="background: #722ed1; color: white; border-radius: 10px; font-size: 10px; padding: 2px 6px; margin-top: 2px; display: inline-block;">15</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">設置</h3>
                                <div style="text-align: left;">
                                    <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                                        <span style="margin-right: 12px; color: #1890ff;">👤</span>
                                        <span style="flex: 1; color: #333;">個人資料</span>
                                        <span style="color: #999;">></span>
                                    </div>
                                    <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                                        <span style="margin-right: 12px; color: #52c41a;">🔒</span>
                                        <span style="flex: 1; color: #333;">安全設置</span>
                                        <span style="color: #999;">></span>
                                    </div>
                                    <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                                        <span style="margin-right: 12px; color: #fa8c16;">🔔</span>
                                        <span style="flex: 1; color: #333;">消息設置</span>
                                        <span style="color: #999;">></span>
                                    </div>
                                    <div style="display: flex; align-items: center; padding: 12px 0; cursor: pointer;">
                                        <span style="margin-right: 12px; color: #722ed1;">ℹ️</span>
                                        <span style="flex: 1; color: #333;">關於我們</span>
                                        <span style="color: #999;">></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                    
                default:
                    break;
            }
            
            // 重新初始化Banner輪播（只在首頁）
            if (tabName === 'home') {
                initBannerCarousel();
            }
        }

        // 重新初始化Banner輪播功能
        function initBannerCarousel() {
            setTimeout(() => {
                currentIndex = 0;
                const newBannerWrapper = document.getElementById('bannerWrapper');
                const newDots = document.querySelectorAll('.dot');
                
                if (newBannerWrapper && newDots.length > 0) {
                    // 重新綁定指示點點擊事件
                    newDots.forEach((dot, index) => {
                        dot.addEventListener('click', () => {
                            currentIndex = index;
                            newBannerWrapper.style.transform = `translateX(-${currentIndex * 100}%)`;
                            newDots.forEach((d, i) => {
                                d.classList.toggle('active', i === currentIndex);
                            });
                        });
                    });
                }
            }, 100);
        }
    </script>
</body>
</html> 