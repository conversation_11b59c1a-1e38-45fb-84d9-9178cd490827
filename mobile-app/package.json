{"name": "shuan-q-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node test-app.js", "test": "node test-api-integration.js", "lint": "echo '<PERSON><PERSON> skipped for demo'"}, "dependencies": {"react": "^18.2.0", "react-native": "^0.72.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.1.0", "@react-navigation/native": "^6.1.0", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/stack": "^6.3.0", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.0", "react-native-vector-icons": "^10.0.0"}, "devDependencies": {"@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.0", "typescript": "4.8.4"}, "keywords": ["react-native", "billiards", "coach", "appointment"], "author": "Your Name", "license": "MIT", "description": "Shuan-Q 台球教练预约移动应用"}