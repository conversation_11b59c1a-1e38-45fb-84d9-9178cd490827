<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQ商城頁面 - 含Tab導航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* 頂部導航欄 */
        .mall-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 56px;
            padding: 0 16px;
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            flex-shrink: 0;
        }

        .nav-button {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 18px;
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .right-buttons {
            display: flex;
            gap: 8px;
        }

        /* 搜索欄 */
        .search-container {
            padding: 12px 16px;
            background: #f8f9fa;
            flex-shrink: 0;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #fff;
            border-radius: 25px;
            padding: 12px 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .search-icon {
            color: #999;
            margin-right: 12px;
            font-size: 18px;
        }

        .search-placeholder {
            color: #999;
            font-size: 16px;
        }

        /* 分類標籤 */
        .category-tabs {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 12px 16px;
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            overflow-x: auto;
            flex-shrink: 0;
        }

        .tabs-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
        }

        .category-tab {
            white-space: nowrap;
            padding: 8px 0;
            position: relative;
            cursor: pointer;
            transition: color 0.2s;
        }

        .category-tab.active {
            color: #1890ff;
            font-weight: 600;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #1890ff;
            border-radius: 1px;
        }

        .category-tab:not(.active) {
            color: #666;
        }

        /* 商品列表容器 */
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        /* 商品列表 */
        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .product-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .product-card:hover {
            transform: translateY(-2px);
        }

        .product-image-container {
            position: relative;
            height: 150px;
            background: linear-gradient(135deg, #e8e8e8, #f5f5f5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
        }

        .product-image-billiard {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }

        .product-image-chalk-purple {
            background: linear-gradient(135deg, #9C27B0, #BA68C8);
            color: white;
        }

        .product-image-chalk-blue {
            background: linear-gradient(135deg, #2196F3, #64B5F6);
            color: white;
        }

        .product-image-orange {
            background: linear-gradient(135deg, #FF9800, #FFB74D);
            color: white;
        }

        .product-image-grey {
            background: linear-gradient(135deg, #607D8B, #90A4AE);
            color: white;
        }

        .hot-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .favorite-button {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            background: rgba(0, 0, 0, 0.3);
            border: none;
            border-radius: 16px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .favorite-button:hover {
            background: rgba(0, 0, 0, 0.5);
        }

        .product-content {
            padding: 12px;
        }

        .product-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.3;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .price-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .price {
            font-size: 18px;
            font-weight: bold;
            color: #ff4757;
        }

        .original-price {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
        }

        .sales-info {
            font-size: 12px;
            color: #999;
        }

        /* 底部Tab導航 */
        .bottom-tabs {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: #fff;
            border-top: 0.5px solid #e5e5e5;
            display: flex;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            padding: 8px 0;
        }

        .tab-item:hover {
            background: rgba(0, 0, 0, 0.02);
        }

        .tab-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .tab-label {
            font-size: 11px;
            font-weight: 400;
        }

        .tab-item.active {
            color: #000000;
        }

        .tab-item.active .tab-icon {
            color: #000000;
        }

        .tab-item:not(.active) {
            color: #999999;
        }

        .tab-item:not(.active) .tab-icon {
            color: #999999;
        }

        /* 響應式設計 */
        @media (max-width: 414px) {
            .phone-mockup {
                width: 100%;
                max-width: 375px;
                height: auto;
                min-height: 600px;
            }
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 頂部導航欄 -->
            <div class="mall-header">
                <div class="nav-button" onclick="alert('返回')">←</div>
                <div class="header-title">SQ商城</div>
                <div class="right-buttons">
                    <div class="nav-button" onclick="alert('更多菜單')">⋯</div>
                </div>
            </div>

            <!-- 搜索欄 -->
            <div class="search-container">
                <div class="search-bar" onclick="alert('打開搜索頁面')">
                    <div class="search-icon">🔍</div>
                    <div class="search-placeholder">搜索商品、教練</div>
                </div>
            </div>

            <!-- 分類標籤 -->
            <div class="category-tabs">
                <div class="tabs-wrapper">
                    <div class="category-tab active">品牌</div>
                    <div class="category-tab">品類</div>
                    <div class="category-tab">銷量</div>
                    <div class="category-tab">價格</div>
                </div>
            </div>

            <!-- 內容區域 -->
            <div class="content-area">
                <!-- 商品網格 -->
                <div class="product-grid">
                    <!-- 商品1 -->
                    <div class="product-card">
                        <div class="product-image-container">
                            商品圖片
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">商品名稱...</div>
                            <div class="price-row">
                                <div class="price">¥888</div>
                                <div class="original-price">¥999</div>
                            </div>
                            <div class="sales-info">10人付款</div>
                        </div>
                    </div>

                    <!-- 商品2 - 一體成型球杆 -->
                    <div class="product-card">
                        <div class="product-image-container product-image-billiard">
                            <div class="hot-badge">熱銷</div>
                            球桌
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">一體成型球杆光滑結頭</div>
                            <div class="price-row">
                                <div class="price">¥888</div>
                                <div class="original-price">¥999</div>
                            </div>
                            <div class="sales-info">10人付款</div>
                        </div>
                    </div>

                    <!-- 商品3 - 職業巧克粉 -->
                    <div class="product-card">
                        <div class="product-image-container product-image-chalk-purple">
                            <div class="hot-badge">熱銷</div>
                            職業巧克粉
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">商品名稱...</div>
                            <div class="price-row">
                                <div class="price">¥888</div>
                                <div class="original-price">¥999</div>
                            </div>
                            <div class="sales-info">10人付款</div>
                        </div>
                    </div>

                    <!-- 商品4 -->
                    <div class="product-card">
                        <div class="product-image-container product-image-chalk-blue">
                            職業巧克粉
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">商品名稱...</div>
                            <div class="price-row">
                                <div class="price">¥888</div>
                                <div class="original-price">¥999</div>
                            </div>
                            <div class="sales-info">10人付款</div>
                        </div>
                    </div>

                    <!-- 商品5 - 專業撞球杆 -->
                    <div class="product-card">
                        <div class="product-image-container product-image-orange">
                            <div class="hot-badge">熱銷</div>
                            專業撞球杆
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">專業撞球杆</div>
                            <div class="price-row">
                                <div class="price">¥1299</div>
                                <div class="original-price">¥1599</div>
                            </div>
                            <div class="sales-info">25人付款</div>
                        </div>
                    </div>

                    <!-- 商品6 - 高級巧克粉套裝 -->
                    <div class="product-card">
                        <div class="product-image-container product-image-grey">
                            巧克粉套裝
                            <button class="favorite-button">♡</button>
                        </div>
                        <div class="product-content">
                            <div class="product-name">高級巧克粉套裝</div>
                            <div class="price-row">
                                <div class="price">¥299</div>
                                <div class="original-price">¥399</div>
                            </div>
                            <div class="sales-info">8人付款</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部Tab導航 -->
            <div class="bottom-tabs">
                <div class="tab-item" onclick="switchTab('home', this)">
                    <div class="tab-icon">🏠</div>
                    <div class="tab-label">首頁</div>
                </div>
                <div class="tab-item active" onclick="switchTab('mall', this)">
                    <div class="tab-icon">🛍️</div>
                    <div class="tab-label">商城</div>
                </div>
                <div class="tab-item" onclick="switchTab('coach', this)">
                    <div class="tab-icon">👨‍🏫</div>
                    <div class="tab-label">教練</div>
                </div>
                <div class="tab-item" onclick="switchTab('profile', this)">
                    <div class="tab-icon">😊</div>
                    <div class="tab-label">我的</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab切換功能
        function switchTab(tabName, element) {
            // 移除所有active狀態
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加當前active狀態
            element.classList.add('active');
            
            // 顯示切換效果
            alert(`切換到${element.querySelector('.tab-label').textContent}頁面`);
        }

        // 分類標籤交互
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 收藏按鈕交互
        document.querySelectorAll('.favorite-button').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                this.innerHTML = this.innerHTML === '♡' ? '♥' : '♡';
                this.style.color = this.innerHTML === '♥' ? '#ff4757' : 'white';
            });
        });

        // 商品卡片點擊
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                const productName = this.querySelector('.product-name').textContent;
                alert(`點擊商品: ${productName}`);
            });
        });
    </script>
</body>
</html> 