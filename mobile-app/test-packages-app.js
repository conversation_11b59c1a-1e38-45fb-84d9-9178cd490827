const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 模拟移动端课程包功能测试
class PackageApp {
  constructor() {
    this.token = null;
    this.currentUser = null;
  }

  // 用户登录
  async login(phone = '13800138000') {
    try {
      console.log('📱 移动端 - 用户登录');
      
      // 发送验证码
      const codeResponse = await axios.post(`${BASE_URL}/auth/send-code`, { phone });
      console.log('✅ 验证码发送成功');
      
      // 登录 (使用固定验证码)
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        phone,
        code: '123456'
      });
      
      this.token = loginResponse.data.data.token;
      this.currentUser = loginResponse.data.data.user;
      
      console.log(`✅ 登录成功: ${this.currentUser.nickname}`);
      return true;
    } catch (error) {
      console.error('❌ 登录失败:', error.response?.data?.error || error.message);
      return false;
    }
  }

  // 获取课程包列表
  async getPackageList(filters = {}) {
    try {
      console.log('\n📱 移动端 - 浏览课程包列表');
      
      const queryParams = new URLSearchParams(filters);
      const response = await axios.get(`${BASE_URL}/packages?${queryParams}`);
      
      const { packages, pagination } = response.data.data;
      
      console.log(`✅ 获取到 ${packages.length} 个课程包`);
      packages.forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.name}`);
        console.log(`   💰 价格: ¥${pkg.price} ${pkg.originalPrice > pkg.price ? `(原价¥${pkg.originalPrice})` : ''}`);
        console.log(`   📚 ${pkg.totalSessions}课时 | ${pkg.validityDays}天有效 | ${pkg.category}`);
        console.log(`   👨‍🏫 教练: ${pkg.coach?.user?.nickname || '未知'}`);
      });
      
      return packages;
    } catch (error) {
      console.error('❌ 获取课程包列表失败:', error.response?.data?.error || error.message);
      return [];
    }
  }

  // 查看课程包详情
  async getPackageDetail(packageId) {
    try {
      console.log(`\n📱 移动端 - 查看课程包详情 (${packageId})`);
      
      const response = await axios.get(`${BASE_URL}/packages/${packageId}`);
      const packageData = response.data.data;

      console.log('✅ 课程包详情:');
      console.log(`📦 名称: ${packageData.name}`);
      console.log(`📝 描述: ${packageData.description}`);
      console.log(`💰 价格: ¥${packageData.price} ${packageData.originalPrice > packageData.price ? `(原价¥${packageData.originalPrice})` : ''}`);
      console.log(`📚 课时: ${packageData.totalSessions}节`);
      console.log(`⏰ 有效期: ${packageData.validityDays}天`);
      console.log(`🏷️ 分类: ${packageData.category} | 级别: ${packageData.level}`);
      console.log(`👨‍🏫 教练: ${packageData.coach?.user?.nickname || '未知'}`);

      if (packageData.features && packageData.features.length > 0) {
        console.log('✨ 课程特色:');
        packageData.features.forEach(feature => {
          console.log(`   • ${feature}`);
        });
      }

      return packageData;
    } catch (error) {
      console.error('❌ 获取课程包详情失败:', error.response?.data?.error || error.message);
      return null;
    }
  }

  // 购买课程包
  async purchasePackage(packageId) {
    try {
      console.log(`\n📱 移动端 - 购买课程包 (${packageId})`);
      
      // 创建订单
      const orderResponse = await axios.post(`${BASE_URL}/orders`, {
        packageId,
        paymentMethod: 'wechat'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const order = orderResponse.data.data;
      console.log('✅ 订单创建成功:');
      console.log(`📋 订单号: ${order.orderNumber}`);
      console.log(`💰 金额: ¥${order.amount}`);
      console.log(`📦 课程包: ${order.package.name}`);
      console.log(`⏰ 过期时间: ${new Date(order.expiredAt).toLocaleString()}`);
      
      // 模拟支付
      console.log('\n💳 正在支付...');
      const payResponse = await axios.post(`${BASE_URL}/orders/${order.id}/pay`, {}, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const paidOrder = payResponse.data.data;
      console.log('✅ 支付成功!');
      console.log(`💳 支付ID: ${paidOrder.paymentId}`);
      console.log(`✅ 状态: ${paidOrder.status}`);
      console.log(`⏰ 支付时间: ${new Date(paidOrder.paidAt).toLocaleString()}`);
      
      return paidOrder;
    } catch (error) {
      console.error('❌ 购买失败:', error.response?.data?.error || error.message);
      return null;
    }
  }

  // 查看我的订单
  async getMyOrders() {
    try {
      console.log('\n📱 移动端 - 查看我的订单');
      
      const response = await axios.get(`${BASE_URL}/orders/my`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      const { orders, pagination } = response.data.data;
      
      console.log(`✅ 共有 ${orders.length} 个订单:`);
      orders.forEach((order, index) => {
        console.log(`${index + 1}. 订单号: ${order.orderNumber}`);
        console.log(`   📦 课程包: ${order.package?.name || '未知'}`);
        console.log(`   💰 金额: ¥${order.amount}`);
        console.log(`   📊 状态: ${order.status}`);
        console.log(`   📅 创建时间: ${new Date(order.createdAt).toLocaleString()}`);
        if (order.paidAt) {
          console.log(`   💳 支付时间: ${new Date(order.paidAt).toLocaleString()}`);
        }
      });
      
      return orders;
    } catch (error) {
      console.error('❌ 获取订单失败:', error.response?.data?.error || error.message);
      return [];
    }
  }

  // 完整的购买流程演示
  async demonstratePurchaseFlow() {
    console.log('🎉 开始演示移动端课程包购买流程\n');
    
    // 1. 用户登录
    const loginSuccess = await this.login();
    if (!loginSuccess) return;
    
    // 2. 浏览课程包列表
    const packages = await this.getPackageList({ category: '基础入门' });
    if (packages.length === 0) return;
    
    // 3. 查看第一个课程包详情
    const firstPackage = packages[0];
    const packageDetail = await this.getPackageDetail(firstPackage.id);
    if (!packageDetail) return;
    
    // 4. 购买课程包
    const order = await this.purchasePackage(firstPackage.id);
    if (!order) return;
    
    // 5. 查看我的订单
    await this.getMyOrders();
    
    console.log('\n🎉 课程包购买流程演示完成!');
    console.log('📱 移动端功能验证成功 ✅');
  }
}

// 运行演示
async function runDemo() {
  const app = new PackageApp();
  await app.demonstratePurchaseFlow();
}

if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = PackageApp;
