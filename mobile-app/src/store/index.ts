import { configureStore } from "@reduxjs/toolkit";
import authSlice from "./slices/authSlice";
import coachSlice from "./slices/coachSlice";
import packageSlice from "./slices/packageSlice";
import orderSlice from "./slices/orderSlice";

export const store = configureStore({
  reducer: {
    auth: authSlice,
    coach: coachSlice,
    packages: packageSlice,
    orders: orderSlice
  }
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
