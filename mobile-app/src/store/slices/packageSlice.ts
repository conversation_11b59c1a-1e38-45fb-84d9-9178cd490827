import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CoursePackage } from '../../../shared/types/Course';

// API 调用
export const fetchPackages = createAsyncThunk(
  'packages/fetchPackages',
  async (params: {
    page?: number;
    limit?: number;
    category?: string;
    level?: string;
    coachId?: string;
    search?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`http://localhost:3000/api/packages?${queryParams}`);
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '获取课程包列表失败');
    }

    return data.data;
  }
);

export const fetchPackageById = createAsyncThunk(
  'packages/fetchPackageById',
  async (packageId: string) => {
    const response = await fetch(`http://localhost:3000/api/packages/${packageId}`);
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '获取课程包详情失败');
    }

    return data.data;
  }
);



interface PackageState {
  packages: CoursePackage[];
  currentPackage: CoursePackage | null;
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

const initialState: PackageState = {
  packages: [],
  currentPackage: null,
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  }
};

const packageSlice = createSlice({
  name: 'packages',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentPackage: (state) => {
      state.currentPackage = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // 获取课程包列表
      .addCase(fetchPackages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPackages.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = action.payload.packages;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchPackages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取课程包列表失败';
      })
      // 获取课程包详情
      .addCase(fetchPackageById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPackageById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentPackage = action.payload;
      })
      .addCase(fetchPackageById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取课程包详情失败';
      });
  }
});

export const { clearError, clearCurrentPackage } = packageSlice.actions;
export default packageSlice.reducer;
