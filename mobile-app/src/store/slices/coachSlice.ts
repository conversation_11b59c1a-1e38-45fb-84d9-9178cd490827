import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

interface Coach {
  id: string;
  user?: {
    nickname: string;
  };
  specialties?: string[];
  experience?: number;
  rating?: number;
  hourlyRate?: number;
  bio?: string;
  studentCount?: number;
}

interface CoachState {
  coaches: Coach[];
  selectedCoach: Coach | null;
  loading: boolean;
  error: string | null;
}

const initialState: CoachState = {
  coaches: [],
  selectedCoach: null,
  loading: false,
  error: null,
};

// API 调用
export const fetchCoaches = createAsyncThunk(
  'coaches/fetchCoaches',
  async (params: {
    page?: number;
    limit?: number;
    search?: string;
    experience?: string;
    rating?: string;
    priceRange?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`http://localhost:3000/api/coaches?${queryParams}`);
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '获取教练列表失败');
    }

    return data.data.coaches;
  }
);

export const fetchCoachById = createAsyncThunk(
  'coaches/fetchCoachById',
  async (coachId: string) => {
    const response = await fetch(`http://localhost:3000/api/coaches/${coachId}`);
    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '获取教练详情失败');
    }

    return data.data;
  }
);

const coachSlice = createSlice({
  name: 'coach',
  initialState,
  reducers: {
    selectCoach: (state, action: PayloadAction<Coach>) => {
      state.selectedCoach = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchCoaches
      .addCase(fetchCoaches.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCoaches.fulfilled, (state, action) => {
        state.loading = false;
        state.coaches = action.payload;
      })
      .addCase(fetchCoaches.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取教练列表失败';
      })
      // fetchCoachById
      .addCase(fetchCoachById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCoachById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedCoach = action.payload;
      })
      .addCase(fetchCoachById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取教练详情失败';
      });
  },
});

export const { selectCoach, clearError } = coachSlice.actions;
export default coachSlice.reducer;
