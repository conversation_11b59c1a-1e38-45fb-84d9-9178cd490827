// 导航参数类型定义

export type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  PackageDetail: { packageId: string };
  CoachDetail: { coachId: string };
  AppointmentCreate: { coachId: string; packageId?: string };
  OrderDetail: { orderId: string };
  Profile: undefined;
  EditProfile: undefined;
  MyOrders: undefined;
  MyAppointments: undefined;
  CoachProfile: undefined;
  ManagePackages: undefined;
  ManageAppointments: undefined;
  StudentManagement: undefined;
};

export type TabParamList = {
  Home: undefined;
  Mall: undefined;
  Coach: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeMain: undefined;
  Search: { type: 'product' | 'coach' };
  ProductDetail: { productId: string };
  CategoryProducts: { categoryId: string; categoryName: string };
};

export type MallStackParamList = {
  MallMain: undefined;
  ProductDetail: { productId: string };
  ProductSearch: undefined;
  CategoryProducts: { categoryId: string; categoryName: string };
};

export type CoachStackParamList = {
  CoachMain: undefined;
  CoachDetail: { coachId: string };
  CoachSearch: undefined;
  PackageDetail: { packageId: string };
  AppointmentCreate: { coachId: string; packageId?: string };
};

export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  MyOrders: undefined;
  MyAppointments: undefined;
  Settings: undefined;
  // 教练专用页面
  CoachProfile: undefined;
  ManagePackages: undefined;
  ManageAppointments: undefined;
  StudentManagement: undefined;
  CreatePackage: undefined;
  EditPackage: { packageId: string };
};
