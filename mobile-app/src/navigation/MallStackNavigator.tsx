import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { MallStackParamList } from './types';

// 导入页面组件
import MallScreen from '../screens/mall/MallScreen';
import ProductDetailScreen from '../screens/product/ProductDetailScreen';
import ProductSearchScreen from '../screens/product/ProductSearchScreen';
import CategoryProductsScreen from '../screens/product/CategoryProductsScreen';

const Stack = createStackNavigator<MallStackParamList>();

const MallStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#5a9178',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="MallMain" 
        component={MallScreen}
        options={{ 
          title: '商城',
          headerShown: false 
        }}
      />
      <Stack.Screen 
        name="ProductDetail" 
        component={ProductDetailScreen}
        options={{ title: '商品详情' }}
      />
      <Stack.Screen 
        name="ProductSearch" 
        component={ProductSearchScreen}
        options={{ title: '商品搜索' }}
      />
      <Stack.Screen 
        name="CategoryProducts" 
        component={CategoryProductsScreen}
        options={({ route }) => ({ 
          title: route.params.categoryName 
        })}
      />
    </Stack.Navigator>
  );
};

export default MallStackNavigator;
