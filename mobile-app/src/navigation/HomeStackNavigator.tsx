import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { HomeStackParamList } from './types';

// 导入页面组件
import HomeScreen from '../screens/home/<USER>';
import SearchScreen from '../screens/search/SearchScreen';
import ProductDetailScreen from '../screens/product/ProductDetailScreen';
import CategoryProductsScreen from '../screens/product/CategoryProductsScreen';

const Stack = createStackNavigator<HomeStackParamList>();

const HomeStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#5a9178',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="HomeMain" 
        component={HomeScreen}
        options={{ 
          title: '首页',
          headerShown: false 
        }}
      />
      <Stack.Screen 
        name="Search" 
        component={SearchScreen}
        options={{ title: '搜索' }}
      />
      <Stack.Screen 
        name="ProductDetail" 
        component={ProductDetailScreen}
        options={{ title: '商品详情' }}
      />
      <Stack.Screen 
        name="CategoryProducts" 
        component={CategoryProductsScreen}
        options={({ route }) => ({ 
          title: route.params.categoryName 
        })}
      />
    </Stack.Navigator>
  );
};

export default HomeStackNavigator;
