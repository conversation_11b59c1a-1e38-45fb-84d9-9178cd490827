import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://your-production-api.com/api';

// API请求封装
class ApiService {
  private async getAuthHeaders() {
    const token = await AsyncStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = await this.getAuthHeaders();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 ${endpoint}:`, error);
      throw error;
    }
  }

  // 认证相关API
  auth = {
    sendCode: async (phone: string) => {
      return this.request('/auth/send-code', {
        method: 'POST',
        body: JSON.stringify({ phone })
      });
    },

    login: async (phone: string, code: string) => {
      const response = await this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ phone, code })
      });

      if (response.success && response.data.token) {
        await AsyncStorage.setItem('token', response.data.token);
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      }

      return response;
    },

    logout: async () => {
      await AsyncStorage.multiRemove(['token', 'user']);
    },

    getProfile: async () => {
      return this.request('/auth/me');
    },

    updateProfile: async (profileData: any) => {
      return this.request('/auth/profile', {
        method: 'PUT',
        body: JSON.stringify(profileData)
      });
    }
  };

  // 教练相关API
  coaches = {
    getList: async (params: {
      page?: number;
      limit?: number;
      location?: string;
      specialty?: string;
      minRating?: number;
      sortBy?: string;
      sortOrder?: string;
    } = {}) => {
      const queryString = new URLSearchParams(
        Object.entries(params).reduce((acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      ).toString();

      return this.request(`/coaches${queryString ? `?${queryString}` : ''}`);
    },

    getDetail: async (id: string) => {
      return this.request(`/coaches/${id}`);
    },

    createProfile: async (profileData: {
      description: string;
      experience: string;
      specialties: string[];
      location: string;
      hourlyRate: number;
    }) => {
      return this.request('/coaches/profile', {
        method: 'POST',
        body: JSON.stringify(profileData)
      });
    },

    updateProfile: async (profileData: any) => {
      return this.request('/coaches/profile', {
        method: 'PUT',
        body: JSON.stringify(profileData)
      });
    }
  };

  // 预约相关API
  appointments = {
    create: async (appointmentData: {
      coachId: string;
      packageId?: string;
      date: string;
      startTime: string;
      endTime: string;
      notes?: string;
    }) => {
      return this.request('/appointments', {
        method: 'POST',
        body: JSON.stringify(appointmentData)
      });
    },

    getMy: async (params: {
      status?: string;
      page?: number;
      limit?: number;
    } = {}) => {
      const queryString = new URLSearchParams(
        Object.entries(params).reduce((acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      ).toString();

      return this.request(`/appointments/my${queryString ? `?${queryString}` : ''}`);
    },

    getDetail: async (id: string) => {
      return this.request(`/appointments/${id}`);
    },

    updateStatus: async (id: string, status: string) => {
      return this.request(`/appointments/${id}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status })
      });
    }
  };
}

export const apiService = new ApiService();

// 导出具体的API方法以便使用
export const authAPI = apiService.auth;
export const coachAPI = apiService.coaches;
export const appointmentAPI = apiService.appointments;

export default apiService;
