import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchPackages } from '../../store/slices/packageSlice';
import { CoursePackage } from '../../../../shared/types/Course';

interface PackageListScreenProps {
  navigation: any;
}

const PackageListScreen: React.FC<PackageListScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { packages, loading, error, pagination } = useSelector((state: RootState) => state.packages);
  
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    level: '',
    page: 1
  });

  useEffect(() => {
    loadPackages();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('错误', error);
    }
  }, [error]);

  const loadPackages = async () => {
    try {
      await dispatch(fetchPackages(filters)).unwrap();
    } catch (err) {
      console.error('加载课程包失败:', err);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPackages();
    setRefreshing(false);
  };

  const renderPackageItem = ({ item }: { item: CoursePackage }) => (
    <TouchableOpacity
      style={styles.packageCard}
      onPress={() => navigation.navigate('PackageDetail', { packageId: item.id })}
    >
      <View style={styles.packageHeader}>
        <Text style={styles.packageName}>{item.name}</Text>
        <View style={styles.levelBadge}>
          <Text style={styles.levelText}>
            {item.level === 'beginner' ? '初级' : 
             item.level === 'intermediate' ? '中级' : '高级'}
          </Text>
        </View>
      </View>
      
      <Text style={styles.packageDescription} numberOfLines={2}>
        {item.description}
      </Text>
      
      <View style={styles.packageInfo}>
        <Text style={styles.category}>{item.category}</Text>
        <Text style={styles.sessions}>{item.totalSessions}课时</Text>
        <Text style={styles.validity}>{item.validityDays}天有效</Text>
      </View>
      
      <View style={styles.priceContainer}>
        {item.originalPrice > item.price && (
          <Text style={styles.originalPrice}>¥{item.originalPrice}</Text>
        )}
        <Text style={styles.price}>¥{item.price}</Text>
      </View>
      
      {item.coach && (
        <View style={styles.coachInfo}>
          <Text style={styles.coachName}>教练: {item.coach.user?.nickname}</Text>
          <Text style={styles.coachRating}>评分: {item.coach.rating}⭐</Text>
        </View>
      )}
      
      <View style={styles.features}>
        {item.features.slice(0, 2).map((feature, index) => (
          <Text key={index} style={styles.feature}>• {feature}</Text>
        ))}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>暂无课程包</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadPackages}>
        <Text style={styles.retryText}>重新加载</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading && packages.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#5a9178" />
        <Text style={styles.loadingText}>加载课程包中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>课程包</Text>
        <Text style={styles.subtitle}>选择适合您的课程包</Text>
      </View>
      
      <FlatList
        data={packages}
        renderItem={renderPackageItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#5a9178']}
          />
        }
        ListEmptyComponent={renderEmptyComponent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4
  },
  subtitle: {
    fontSize: 14,
    color: '#666'
  },
  listContainer: {
    padding: 16
  },
  packageCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3
  },
  packageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1
  },
  levelBadge: {
    backgroundColor: '#5a9178',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  levelText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500'
  },
  packageDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12
  },
  packageInfo: {
    flexDirection: 'row',
    marginBottom: 12
  },
  category: {
    fontSize: 12,
    color: '#5a9178',
    backgroundColor: '#f0f8f5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8
  },
  sessions: {
    fontSize: 12,
    color: '#007bff',
    backgroundColor: '#e7f3ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8
  },
  validity: {
    fontSize: 12,
    color: '#ffc107',
    backgroundColor: '#fff8e1',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  originalPrice: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'line-through',
    marginRight: 8
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c'
  },
  coachInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  coachName: {
    fontSize: 14,
    color: '#333'
  },
  coachRating: {
    fontSize: 14,
    color: '#ffc107'
  },
  features: {
    marginTop: 8
  },
  feature: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16
  },
  retryButton: {
    backgroundColor: '#5a9178',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8
  },
  retryText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500'
  }
});

export default PackageListScreen;
