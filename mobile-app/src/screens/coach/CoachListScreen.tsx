import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { HomeStackParamList } from '../../navigation/types';
import CoachHeader from '../../components/CoachHeader';
import CoachFilterTabs from '../../components/CoachFilterTabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

type CoachListScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'CoachList'>;
type CoachListScreenRouteProp = RouteProp<HomeStackParamList, 'CoachList'>;

interface Props {
  navigation: CoachListScreenNavigationProp;
  route: CoachListScreenRouteProp;
}

const CoachListScreen: React.FC<Props> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [activeCategory, setActiveCategory] = useState('全部');

  // Filter categories
  const categories = ['全部', '初級教練', '中級教練', '高級教練', '金牌教練'];

  // Mock coach data
  const coaches = [
    {
      id: '1',
      name: '張偉教練',
      avatar: require('../../../images/coaches/coach1.jpg'),
      level: '金牌教練',
      experience: '8年教學經驗',
      rating: 4.9,
      studentCount: 256,
      specialties: ['基礎教學', '技巧提升', '比賽指導'],
      pricePerHour: 200,
      isOnline: true,
      description: '專業台球教練，擅長基礎教學和技巧提升，已培養多名專業選手。',
    },
    {
      id: '2',
      name: '李明教練',
      avatar: require('../../../images/coaches/coach2.jpg'),
      level: '高級教練',
      experience: '6年教學經驗',
      rating: 4.8,
      studentCount: 189,
      specialties: ['進階技巧', '戰術分析'],
      pricePerHour: 180,
      isOnline: false,
      description: '技術全面，戰術分析能力強，善於幫助學員突破技術瓶頸。',
    },
    {
      id: '3',
      name: '王芳教練',
      avatar: require('../../../images/coaches/coach3.jpg'),
      level: '中級教練',
      experience: '4年教學經驗',
      rating: 4.7,
      studentCount: 145,
      specialties: ['女性教學', '基礎入門'],
      pricePerHour: 150,
      isOnline: true,
      description: '女性教練，溫柔耐心，特別擅長女性學員的基礎教學。',
    },
    {
      id: '4',
      name: '陳强教練',
      avatar: require('../../../images/coaches/coach4.jpg'),
      level: '金牌教練',
      experience: '10年教學經驗',
      rating: 5.0,
      studentCount: 312,
      specialties: ['競賽培訓', '專業選手指導'],
      pricePerHour: 250,
      isOnline: true,
      description: '資深教練，曾培養多名全國冠軍，專業競賽培訓經驗豐富。',
    },
    {
      id: '5',
      name: '劉濤教練',
      avatar: require('../../../images/coaches/coach5.jpg'),
      level: '高級教練',
      experience: '5年教學經驗',
      rating: 4.6,
      studentCount: 167,
      specialties: ['青少年教學', '基礎糾正'],
      pricePerHour: 160,
      isOnline: false,
      description: '專注青少年台球教學，耐心細心，深受家長和學員喜愛。',
    },
    {
      id: '6',
      name: '趙敏教練',
      avatar: require('../../../images/coaches/coach6.jpg'),
      level: '中級教練',
      experience: '3年教學經驗',
      rating: 4.5,
      studentCount: 98,
      specialties: ['入門教學', '興趣培養'],
      pricePerHour: 120,
      isOnline: true,
      description: '年輕有活力，善於激發學員興趣，讓學習台球變得有趣。',
    },
  ];

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSearch = () => {
    navigation.navigate('CoachSearch');
  };

  const handleFilter = () => {
    // Handle filter modal
  };

  const handleCoachPress = (coachId: string) => {
    navigation.navigate('CoachDetail', { coachId });
  };

  const handleBooking = (coachId: string) => {
    navigation.navigate('AppointmentCreate', { coachId });
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case '金牌教練':
        return '#fadb14';
      case '高級教練':
        return '#52c41a';
      case '中級教練':
        return '#1890ff';
      case '初級教練':
        return '#722ed1';
      default:
        return '#999';
    }
  };

  const filteredCoaches = activeCategory === '全部' 
    ? coaches 
    : coaches.filter(coach => coach.level === activeCategory);

  const renderCoach = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.coachCard}
      onPress={() => handleCoachPress(item.id)}
    >
      <View style={styles.coachHeader}>
        <View style={styles.avatarContainer}>
          <Image source={item.avatar} style={styles.avatar} resizeMode="cover" />
          <View style={[styles.onlineStatus, { backgroundColor: item.isOnline ? '#52c41a' : '#999' }]} />
        </View>
        
        <View style={styles.coachInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.coachName}>{item.name}</Text>
            <View style={[styles.levelBadge, { backgroundColor: getLevelColor(item.level) }]}>
              <Text style={styles.levelText}>{item.level}</Text>
            </View>
          </View>
          
          <Text style={styles.experience}>{item.experience}</Text>
          
          <View style={styles.ratingRow}>
            <Icon name="star" size={16} color="#fadb14" />
            <Text style={styles.rating}>{item.rating}</Text>
            <Text style={styles.studentCount}>({item.studentCount}位學員)</Text>
          </View>
        </View>
        
        <View style={styles.priceContainer}>
          <Text style={styles.price}>¥{item.pricePerHour}</Text>
          <Text style={styles.priceUnit}>/小時</Text>
        </View>
      </View>
      
      <Text style={styles.description} numberOfLines={2}>{item.description}</Text>
      
      <View style={styles.specialtiesContainer}>
        {item.specialties.map((specialty: string, index: number) => (
          <View key={index} style={styles.specialtyTag}>
            <Text style={styles.specialtyText}>{specialty}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.actionRow}>
        <TouchableOpacity 
          style={styles.chatButton}
          onPress={() => {/* Handle chat */}}
        >
          <Icon name="chat" size={18} color="#1890ff" />
          <Text style={styles.chatText}>諮詢</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.bookButton}
          onPress={() => handleBooking(item.id)}
        >
          <Text style={styles.bookText}>立即預約</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <CoachHeader 
        onBack={handleBack}
        onSearch={handleSearch}
        onFilter={handleFilter}
      />
      
      <CoachFilterTabs
        categories={categories}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
      />
      
      <FlatList
        data={filteredCoaches}
        renderItem={renderCoach}
        keyExtractor={(item) => item.id}
        style={styles.coachList}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  coachList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 16,
  },
  coachCard: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  coachHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineStatus: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#fff',
  },
  coachInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  coachName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  levelBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  levelText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  experience: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 12,
    color: '#333',
    marginLeft: 4,
  },
  studentCount: {
    fontSize: 10,
    color: '#999',
    marginLeft: 4,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff4757',
  },
  priceUnit: {
    fontSize: 12,
    color: '#999',
  },
  description: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
    marginBottom: 12,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  specialtyTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  specialtyText: {
    fontSize: 11,
    color: '#666',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#1890ff',
  },
  chatText: {
    fontSize: 14,
    color: '#1890ff',
    marginLeft: 4,
  },
  bookButton: {
    backgroundColor: '#1890ff',
    paddingVertical: 10,
    paddingHorizontal: 24,
    borderRadius: 20,
  },
  bookText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CoachListScreen;

