import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { fetchCoaches } from '../../store/slices/coachSlice';

// 导入组件
import SearchBar from '../../components/SearchBar';
import CoachCard from '../../components/CoachCard';

const CoachSearchScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();
  
  const { query: initialQuery } = route.params as any;
  const [searchQuery, setSearchQuery] = useState(initialQuery || '');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const { coaches } = useSelector((state: RootState) => state.coach);

  useEffect(() => {
    if (initialQuery) {
      handleSearch(initialQuery);
    }
  }, [initialQuery]);

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setSearchQuery(query);

    try {
      await dispatch(fetchCoaches({ search: query }) as any);
      
      // 过滤教练数据
      const filteredCoaches = coaches.filter(coach =>
        coach.user?.nickname.toLowerCase().includes(query.toLowerCase()) ||
        coach.specialties?.some(specialty => 
          specialty.toLowerCase().includes(query.toLowerCase())
        ) ||
        coach.bio?.toLowerCase().includes(query.toLowerCase())
      );
      
      setResults(filteredCoaches);
    } catch (error) {
      console.error('搜索教练失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCoachPress = (coach: any) => {
    navigation.navigate('CoachDetail' as never, { coachId: coach.id });
  };

  const renderCoachItem = ({ item }: { item: any }) => (
    <CoachCard
      coach={item}
      onPress={() => handleCoachPress(item)}
    />
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {searchQuery ? '没有找到相关教练' : '请输入搜索关键词'}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="搜索教练姓名、专长..."
          onSearch={handleSearch}
          value={searchQuery}
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5a9178" />
          <Text style={styles.loadingText}>搜索中...</Text>
        </View>
      ) : (
        <FlatList
          data={results}
          renderItem={renderCoachItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyComponent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
});

export default CoachSearchScreen;
