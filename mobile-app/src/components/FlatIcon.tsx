import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, Circle, Rect, Polygon } from 'react-native-svg';

interface FlatIconProps {
  name: string;
  size: number;
  color: string;
  focused: boolean;
}

const FlatIcon: React.FC<FlatIconProps> = ({ name, size, color, focused }) => {
  const iconSize = focused ? size + 2 : size;
  
  const renderIcon = () => {
    switch (name) {
      case 'Home':
        return (
          <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24">
            <Path
              d="M12 3L20 9V21H15V16H9V21H4V9L12 3Z"
              fill={color}
              stroke="none"
            />
          </Svg>
        );
      
      case 'Mall':
        return (
          <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24">
            <Path
              d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"
              fill={color}
              stroke="none"
            />
            <Circle cx="10" cy="12" r="1" fill={color} />
            <Circle cx="14" cy="12" r="1" fill={color} />
          </Svg>
        );
      
      case 'Coach':
        return (
          <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24">
            <Circle cx="12" cy="8" r="4" fill={color} />
            <Path
              d="M12 14C8 14 4 16 4 18V20H20V18C20 16 16 14 12 14Z"
              fill={color}
              stroke="none"
            />
          </Svg>
        );
      
      case 'Profile':
        return (
          <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24">
            <Circle cx="12" cy="12" r="10" fill={color} />
            <Circle cx="9" cy="10" r="1.5" fill="white" />
            <Circle cx="15" cy="10" r="1.5" fill="white" />
            <Path
              d="M8 15C8.5 16 10 17 12 17C14 17 15.5 16 16 15"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              fill="none"
            />
          </Svg>
        );
      
      default:
        return (
          <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24">
            <Circle cx="12" cy="12" r="10" fill={color} />
            <Path
              d="M12 8V16M8 12H16"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </Svg>
        );
    }
  };

  return (
    <View style={[styles.container, focused && styles.focused]}>
      {renderIcon()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
  },
  focused: {
    transform: [{ scale: 1.1 }],
  },
});

export default FlatIcon; 