import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Coach {
  id: string;
  user?: {
    nickname: string;
  };
  specialties?: string[];
  experience?: number;
  rating?: number;
  hourlyRate?: number;
  bio?: string;
}

interface CoachCardProps {
  coach: Coach;
  onPress: () => void;
}

const CoachCard: React.FC<CoachCardProps> = ({ coach, onPress }) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.avatar}>
          <Icon name="person" size={32} color="#5a9178" />
        </View>
        <View style={styles.info}>
          <Text style={styles.name}>{coach.user?.nickname || '未知教练'}</Text>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#faad14" />
            <Text style={styles.rating}>{coach.rating || 5.0}</Text>
            <Text style={styles.experience}>
              {coach.experience || 0}年经验
            </Text>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>¥{coach.hourlyRate || 100}</Text>
          <Text style={styles.priceUnit}>/小时</Text>
        </View>
      </View>

      {coach.specialties && coach.specialties.length > 0 && (
        <View style={styles.specialtiesContainer}>
          {coach.specialties.slice(0, 3).map((specialty, index) => (
            <View key={index} style={styles.specialtyTag}>
              <Text style={styles.specialtyText}>{specialty}</Text>
            </View>
          ))}
        </View>
      )}

      {coach.bio && (
        <Text style={styles.bio} numberOfLines={2}>
          {coach.bio}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#f0f8f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    color: '#333',
    marginLeft: 4,
    marginRight: 8,
  },
  experience: {
    fontSize: 14,
    color: '#666',
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5222d',
  },
  priceUnit: {
    fontSize: 12,
    color: '#666',
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  specialtyTag: {
    backgroundColor: '#f0f8f5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  specialtyText: {
    fontSize: 12,
    color: '#5a9178',
  },
  bio: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default CoachCard;
