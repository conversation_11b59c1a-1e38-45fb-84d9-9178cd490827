import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface HomeSearchBarProps {
  placeholder?: string;
  onPress?: () => void;
  onSearch?: (query: string) => void;
  editable?: boolean;
  value?: string;
}

const HomeSearchBar: React.FC<HomeSearchBarProps> = ({
  placeholder = "搜索課程包、教練...",
  onPress,
  onSearch,
  editable = false,
  value,
}) => {
  if (!editable) {
    return (
      <View style={styles.container}>
        <TouchableOpacity style={styles.searchBar} onPress={onPress}>
          <Icon name="search" size={20} color="#999" style={styles.searchIcon} />
          <Text style={styles.placeholder}>{placeholder}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchBar}>
        <Icon name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor="#999"
          value={value}
          onChangeText={onSearch}
          returnKeyType="search"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#5a9178',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backdropFilter: 'blur(10px)',
  },
  searchIcon: {
    marginRight: 12,
  },
  placeholder: {
    fontSize: 16,
    color: '#999',
    flex: 1,
  },
  input: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    padding: 0,
  },
});

export default HomeSearchBar; 