import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ProfileHeaderProps {
  onSettings?: () => void;
  onNotification?: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  onSettings,
  onNotification,
}) => {
  return (
    <>
      <StatusBar backgroundColor="#722ed1" barStyle="light-content" />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>我的</Text>
          
          <View style={styles.rightButtons}>
            <TouchableOpacity style={styles.rightButton} onPress={onNotification}>
              <Icon name="notifications-none" size={24} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.rightButton} onPress={onSettings}>
              <Icon name="settings" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#722ed1',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default ProfileHeader; 