import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface HomeHeaderProps {
  onProfile?: () => void;
  onNotification?: () => void;
}

const HomeHeader: React.FC<HomeHeaderProps> = ({
  onProfile,
  onNotification,
}) => {
  return (
    <>
      <StatusBar backgroundColor="#5a9178" barStyle="light-content" />
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.leftSection}>
            <Text style={styles.greeting}>你好！</Text>
            <Text style={styles.title}>SQ台球教學</Text>
          </View>
          
          <View style={styles.rightButtons}>
            <TouchableOpacity style={styles.rightButton} onPress={onNotification}>
              <Icon name="notifications-none" size={24} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.rightButton} onPress={onProfile}>
              <Icon name="person-outline" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#5a9178',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: 16,
  },
  leftSection: {
    flex: 1,
  },
  greeting: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default HomeHeader; 