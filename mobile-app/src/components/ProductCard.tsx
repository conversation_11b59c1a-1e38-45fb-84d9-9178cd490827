import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2; // 16px margin + 16px gap

interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  totalSessions: number;
  validityDays: number;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  coach?: {
    user?: {
      nickname: string;
    };
  };
}

interface ProductCardProps {
  package: Package;
  onPress: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ package: pkg, onPress }) => {
  const getLevelText = (level: string) => {
    switch (level) {
      case 'beginner':
        return '初级';
      case 'intermediate':
        return '中级';
      case 'advanced':
        return '高级';
      default:
        return level;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return '#52c41a';
      case 'intermediate':
        return '#faad14';
      case 'advanced':
        return '#f5222d';
      default:
        return '#666';
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri: `https://via.placeholder.com/200x120/5a9178/ffffff?text=${encodeURIComponent(pkg.name)}`,
          }}
          style={styles.image}
        />
        <View style={[styles.levelBadge, { backgroundColor: getLevelColor(pkg.level) }]}>
          <Text style={styles.levelText}>{getLevelText(pkg.level)}</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={2}>
          {pkg.name}
        </Text>
        
        <Text style={styles.description} numberOfLines={2}>
          {pkg.description}
        </Text>

        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Icon name="schedule" size={14} color="#666" />
            <Text style={styles.infoText}>{pkg.totalSessions}课时</Text>
          </View>
          <View style={styles.infoItem}>
            <Icon name="access-time" size={14} color="#666" />
            <Text style={styles.infoText}>{pkg.validityDays}天</Text>
          </View>
        </View>

        <View style={styles.categoryRow}>
          <Text style={styles.category}>{pkg.category}</Text>
          {pkg.coach?.user?.nickname && (
            <Text style={styles.coach}>{pkg.coach.user.nickname}</Text>
          )}
        </View>

        <View style={styles.priceRow}>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>¥{pkg.price}</Text>
            {pkg.originalPrice && pkg.originalPrice > pkg.price && (
              <Text style={styles.originalPrice}>¥{pkg.originalPrice}</Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: CARD_WIDTH,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    marginHorizontal: 4,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  levelBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  levelText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  content: {
    padding: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
    lineHeight: 20,
  },
  description: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
    lineHeight: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  category: {
    fontSize: 12,
    color: '#5a9178',
    backgroundColor: '#f0f8f5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  coach: {
    fontSize: 12,
    color: '#666',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f5222d',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
});

export default ProductCard;
