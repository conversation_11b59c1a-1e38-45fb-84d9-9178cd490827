import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface QuickActionItem {
  id: string;
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
}

interface QuickActionsProps {
  data: QuickActionItem[];
  numColumns?: number;
}

const QuickActions: React.FC<QuickActionsProps> = ({
  data,
  numColumns = 4,
}) => {
  const renderItem = ({ item }: { item: QuickActionItem }) => (
    <TouchableOpacity style={styles.actionItem} onPress={item.onPress}>
      <View style={styles.iconContainer}>
        <Icon name={item.icon} size={24} color="#5a9178" />
      </View>
      <Text style={styles.actionTitle}>{item.title}</Text>
      {item.subtitle && (
        <Text style={styles.actionSubtitle}>{item.subtitle}</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={numColumns}
        scrollEnabled={false}
        contentContainerStyle={styles.grid}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    marginHorizontal: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  grid: {
    justifyContent: 'space-between',
  },
  actionItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f0f8f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default QuickActions;
