// 移动端API集成测试
const http = require('http');

const API_BASE_URL = 'http://localhost:3000';

// 模拟fetch API
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            json: () => Promise.resolve(jsonData)
          });
        } catch (e) {
          resolve({
            ok: false,
            status: res.statusCode,
            json: () => Promise.resolve({ error: 'Invalid JSON' })
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// 模拟移动端API服务类
class MobileApiService {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`❌ API请求失败 ${endpoint}:`, error.message);
      throw error;
    }
  }

  // 认证相关API
  async sendCode(phone) {
    console.log(`📱 发送验证码到: ${phone}`);
    return this.request('/api/auth/send-code', {
      method: 'POST',
      body: JSON.stringify({ phone })
    });
  }

  async login(phone, code) {
    console.log(`🔐 用户登录: ${phone}`);
    return this.request('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ phone, code })
    });
  }

  // 教练相关API
  async getCoaches(params = {}) {
    console.log(`👨‍🏫 获取教练列表`);
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/api/coaches${queryString ? `?${queryString}` : ''}`);
  }

  async getCoachDetail(id) {
    console.log(`📋 获取教练详情: ${id}`);
    return this.request(`/api/coaches/${id}`);
  }

  // 预约相关API
  async createAppointment(appointmentData) {
    console.log(`📅 创建预约`);
    return this.request('/api/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData)
    });
  }
}

// 模拟移动端应用状态管理
class MobileAppState {
  constructor() {
    this.user = null;
    this.token = null;
    this.coaches = [];
    this.appointments = [];
  }

  setUser(user, token) {
    this.user = user;
    this.token = token;
    console.log(`✅ 用户状态更新:`, user);
  }

  setCoaches(coaches) {
    this.coaches = coaches;
    console.log(`✅ 教练列表更新: ${coaches.length} 个教练`);
  }

  addAppointment(appointment) {
    this.appointments.push(appointment);
    console.log(`✅ 预约添加:`, appointment);
  }

  getState() {
    return {
      user: this.user,
      token: this.token,
      coaches: this.coaches,
      appointments: this.appointments
    };
  }
}

// 集成测试主函数
async function runIntegrationTests() {
  console.log('🧪 开始移动端API集成测试');
  console.log('================================');

  const apiService = new MobileApiService(API_BASE_URL);
  const appState = new MobileAppState();

  try {
    // 测试1: 发送验证码
    console.log('\n📱 测试1: 发送验证码');
    const sendCodeResult = await apiService.sendCode('13800138000');
    console.log('✅ 验证码发送成功:', sendCodeResult.message);

    // 测试2: 用户登录
    console.log('\n🔐 测试2: 用户登录');
    const loginResult = await apiService.login('13800138000', '123456');
    appState.setUser(loginResult.data.user, loginResult.data.token);

    // 测试3: 获取教练列表
    console.log('\n👨‍🏫 测试3: 获取教练列表');
    const coachesResult = await apiService.getCoaches();
    appState.setCoaches(coachesResult.data.coaches);

    // 测试4: 获取教练详情
    console.log('\n📋 测试4: 获取教练详情');
    const coachDetail = await apiService.getCoachDetail('1');
    console.log('✅ 教练详情:', coachDetail.data.name, '- 评分:', coachDetail.data.rating);

    // 测试5: 创建预约
    console.log('\n📅 测试5: 创建预约');
    const appointmentData = {
      coachId: '1',
      date: '2024-01-15',
      startTime: '14:00',
      endTime: '15:00',
      notes: '想学习斯诺克基础技巧'
    };
    const appointmentResult = await apiService.createAppointment(appointmentData);
    appState.addAppointment(appointmentResult.data);

    // 测试6: 应用状态检查
    console.log('\n📊 测试6: 应用状态检查');
    const finalState = appState.getState();
    console.log('✅ 最终应用状态:');
    console.log(`   - 用户: ${finalState.user.nickname} (${finalState.user.userType})`);
    console.log(`   - Token: ${finalState.token.substring(0, 20)}...`);
    console.log(`   - 教练数量: ${finalState.coaches.length}`);
    console.log(`   - 预约数量: ${finalState.appointments.length}`);

    console.log('\n🎉 所有集成测试通过！');
    console.log('================================');
    console.log('✅ 移动端API集成功能正常');
    console.log('✅ 状态管理工作正常');
    console.log('✅ 数据流转完整');

  } catch (error) {
    console.error('\n❌ 集成测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runIntegrationTests();
}

module.exports = { MobileApiService, MobileAppState };
