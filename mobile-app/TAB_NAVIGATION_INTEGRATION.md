# Tab導航集成指南 - SQ商城頁面

## 概述

本文檔說明如何在SQ商城頁面中正確集成底部Tab導航，確保所有頁面都能顯示統一的Tab欄，並提供無縫的導航體驗。

## Tab導航架構

### 🗂️ 導航結構
```
TabNavigator (主容器)
├── HomeStackNavigator (首頁)
├── MallStackNavigator (商城) ← 當前頁面
├── CoachStackNavigator (教練)
└── ProfileStackNavigator (我的)
```

### 📱 Tab配置
| Tab | 名稱 | 圖標 | 路由名 | 組件 |
|-----|------|------|--------|------|
| 1 | 首頁 | 🏠 | Home | HomeStackNavigator |
| 2 | 商城 | 🛍️ | Mall | MallStackNavigator |
| 3 | 教練 | 👨‍🏫 | Coach | CoachStackNavigator |
| 4 | 我的 | 😊 | Profile | ProfileStackNavigator |

## 商城頁面Tab集成

### ✅ 正確的頁面結構

```tsx
// MallScreen.tsx - 正確的結構
const MallScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      {/* 頂部導航欄 */}
      <MallHeader />
      
      {/* 搜索欄 */}
      <MallSearchBar />
      
      {/* 分類標籤 */}
      <CategoryTabs />
      
      {/* 商品列表 - 主要內容區域 */}
      <FlatList
        // ... 商品列表配置
        contentContainerStyle={styles.productList}
      />
      
      {/* ❌ 不需要手動添加底部Tab - 由TabNavigator自動處理 */}
    </SafeAreaView>
  );
};
```

### 🎯 關鍵樣式調整

```tsx
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    // ❌ 不需要底部padding - TabNavigator會自動處理
  },
  productList: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16, // ✅ 減少底部間距，Tab導航器會自動處理
  },
});
```

### ⚠️ 避免的錯誤

❌ **錯誤做法1 - 手動添加Tab欄**:
```tsx
// 不要這樣做
return (
  <SafeAreaView>
    <MallContent />
    <CustomTabBar /> {/* ❌ 會與TabNavigator衝突 */}
  </SafeAreaView>
);
```

❌ **錯誤做法2 - 過多的底部間距**:
```tsx
const styles = StyleSheet.create({
  productList: {
    paddingBottom: 80, // ❌ 過多間距會造成內容被遮擋
  },
});
```

## TabNavigator配置詳解

### 🎨 視覺樣式
```tsx
// TabNavigator.tsx 配置
<Tab.Navigator
  screenOptions={{
    tabBarActiveTintColor: '#000000',    // 選中狀態：黑色
    tabBarInactiveTintColor: '#999999',  // 未選中：灰色
    tabBarStyle: {
      backgroundColor: '#ffffff',        // 背景：白色
      borderTopWidth: 0.5,              // 頂部邊框
      borderTopColor: '#e5e5e5',        // 邊框顏色
      paddingBottom: 8,                 // 底部內邊距
      paddingTop: 8,                    // 頂部內邊距
      height: 70,                       // Tab欄高度
      shadowColor: '#000',              // 陰影效果
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.05,
      shadowRadius: 8,
      elevation: 8,
    },
    tabBarLabelStyle: {
      fontSize: 11,                     // 標籤字體大小
      fontWeight: '400',                // 標籤字重
      marginTop: 2,                     // 標籤頂部間距
    },
    headerShown: false,                 // 隱藏默認標題欄
  }}
>
```

### 🎭 圖標系統
```tsx
// TabIcon.tsx - 扁平化圖標組件
const TabIcon: React.FC<TabIconProps> = ({ name, focused, color, size }) => {
  return (
    <FlatIcon 
      name={name}
      size={size}
      color={color}
      focused={focused}
    />
  );
};
```

## 頁面間導航

### 🔄 頁面切換流程
1. **用戶點擊Tab** → Tab狀態變更
2. **路由切換** → 對應Stack Navigator激活
3. **頁面渲染** → 新頁面內容載入
4. **狀態保持** → 前一頁面狀態保存

### 📍 導航示例
```tsx
// 在商城頁面中跳轉到其他Tab
const navigation = useNavigation();

// 跳轉到首頁
navigation.navigate('Home');

// 跳轉到教練頁面
navigation.navigate('Coach');

// 跳轉到我的頁面
navigation.navigate('Profile');
```

## Stack Navigator集成

### 🏗️ MallStackNavigator配置
```tsx
// MallStackNavigator.tsx
const MallStack = createStackNavigator();

const MallStackNavigator: React.FC = () => {
  return (
    <MallStack.Navigator
      screenOptions={{
        headerShown: false, // 隱藏默認標題，使用自定義MallHeader
      }}
    >
      <MallStack.Screen name="MallMain" component={MallScreen} />
      <MallStack.Screen name="ProductDetail" component={ProductDetailScreen} />
      <MallStack.Screen name="ProductSearch" component={ProductSearchScreen} />
      {/* 其他商城相關頁面 */}
    </MallStack.Navigator>
  );
};
```

### 🧭 頁面層次關係
```
TabNavigator
└── MallStackNavigator
    ├── MallScreen (主商城頁面) ← 顯示Tab欄
    ├── ProductDetailScreen (商品詳情) ← 顯示Tab欄
    ├── ProductSearchScreen (搜索頁面) ← 顯示Tab欄
    └── ... (其他頁面)
```

## 響應式設計

### 📱 適配不同屏幕尺寸
```tsx
// 自適應Tab欄高度
const styles = StyleSheet.create({
  tabBarStyle: {
    height: Platform.OS === 'ios' ? 70 : 60, // iOS/Android差異化
    paddingBottom: Platform.OS === 'ios' ? 8 : 4,
  },
});
```

### 🔄 橫豎屏適配
- **豎屏模式**: 標準70px高度Tab欄
- **橫屏模式**: 可選擇隱藏Tab欄或調整高度

## 性能優化

### ⚡ 渲染優化
1. **懶加載**: 非當前Tab頁面延遲渲染
2. **狀態保持**: 使用`unmountOnBlur: false`保持頁面狀態
3. **圖標優化**: 使用SVG圖標減少內存占用

### 🎯 用戶體驗優化
1. **切換動畫**: 平滑的Tab切換過渡
2. **狀態指示**: 清晰的選中狀態視覺反饋
3. **觸摸反應**: 適當的觸摸回饋效果

## 調試指南

### 🔍 常見問題排查

**問題1**: Tab欄不顯示
```tsx
// 檢查是否正確使用TabNavigator包裹
<NavigationContainer>
  <TabNavigator /> {/* ✅ 確保在最外層 */}
</NavigationContainer>
```

**問題2**: 頁面內容被遮擋
```tsx
// 檢查SafeAreaView和padding設置
<SafeAreaView style={{ flex: 1 }}>
  {/* 內容區域 */}
  <FlatList
    contentContainerStyle={{
      paddingBottom: 16, // ✅ 適當的底部間距
    }}
  />
</SafeAreaView>
```

**問題3**: Tab切換異常
```tsx
// 檢查路由名稱是否正確
<Tab.Screen 
  name="Mall" // ✅ 確保名稱與導航調用一致
  component={MallStackNavigator}
/>
```

## 部署檢查清單

### ✅ 發布前檢查
- [ ] Tab欄在所有頁面正常顯示
- [ ] Tab切換動畫流暢
- [ ] 圖標顯示正確
- [ ] 標籤文字無截斷
- [ ] 不同設備尺寸適配正常
- [ ] 狀態保持功能正常
- [ ] 性能表現良好

## 總結

通過正確集成TabNavigator，SQ商城頁面現在具備了：

✅ **統一的導航體驗** - 所有頁面都顯示相同的Tab欄
✅ **無縫的頁面切換** - 平滑的過渡動畫和狀態保持
✅ **響應式設計** - 適配不同屏幕尺寸和方向
✅ **優化的性能** - 懶加載和高效的渲染機制

這為後續其他頁面的開發提供了標準化的導航框架，確保整個應用的一致性和可維護性。 