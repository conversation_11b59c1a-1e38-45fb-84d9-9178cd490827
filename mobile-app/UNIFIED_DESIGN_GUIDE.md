# SQ台球教學 - 統一設計指南

## 🎨 設計概述

本指南定義了SQ台球教學移動應用四大核心頁面（首頁、商城、教練、我的）的統一設計標準，確保整個應用具有一致的用戶體驗和視覺風格。

## 📱 四大頁面概覽

### 1. 首頁 (HomeScreen)
- **主色調**: `#5a9178` (綠色系)
- **功能**: 輪播橫幅、快速操作、熱門課程包展示
- **特色**: 漸變背景、自動輪播、卡片式佈局

### 2. 商城 (MallScreen)
- **主色調**: `#1890ff` (藍色系)
- **功能**: 商品分類、產品展示、購物功能
- **特色**: 居中分類標籤、2列商品網格、熱銷標識

### 3. 教練 (CoachListScreen)
- **主色調**: `#1890ff` (藍色系)
- **功能**: 教練篩選、教練卡片、預約諮詢
- **特色**: 等級徽章、在線狀態、專業標籤

### 4. 我的 (ProfileScreen)
- **主色調**: `#722ed1` (紫色系)
- **功能**: 個人信息、數據統計、功能入口
- **特色**: 餘額展示、功能模塊、設置選項

## 🎯 統一設計標準

### 顏色規範

#### 主色調
```css
首頁綠色: #5a9178
商城藍色: #1890ff
教練藍色: #1890ff
我的紫色: #722ed1
```

#### 輔助色彩
```css
成功綠色: #52c41a
警告橙色: #fa8c16
錯誤紅色: #ff4757
收藏粉色: #eb2f96
黃金色彩: #fadb14
```

#### 灰度色彩
```css
主文字: #333333
副文字: #666666
提示文字: #999999
邊框色: #e1e1e1
背景色: #f8f9fa
```

### 組件規範

#### 1. 頂部導航欄 (Header)
```tsx
通用規格:
- 高度: 56px
- 內邊距: 0 16px
- 文字大小: 18px
- 文字顏色: #ffffff
- 按鈕尺寸: 40x40px
```

#### 2. 搜索欄 (SearchBar)
```tsx
設計規格:
- 背景: rgba(255,255,255,0.9)
- 圓角: 25px
- 內邊距: 12px 16px
- 圖標大小: 20px
- 圖標顏色: #999999
```

#### 3. 分類標籤 (CategoryTabs)
```tsx
標籤規格:
- 內邊距: 8px 20px
- 圓角: 20px
- 最小寬度: 80px
- 非活躍背景: #f5f5f5
- 活躍背景: 對應頁面主色
- 標籤間距: 40px (居中對齊)
```

#### 4. 卡片組件 (Cards)
```tsx
卡片規格:
- 圓角: 12px
- 陰影: elevation: 2
- 內邊距: 12-16px
- 邊距: 16px (水平), 12px (垂直)
- 背景: #ffffff
```

#### 5. 按鈕組件 (Buttons)
```tsx
主按鈕:
- 背景: 對應頁面主色
- 文字: #ffffff
- 圓角: 20px
- 內邊距: 10px 24px

次按鈕:
- 邊框: 1px solid 主色
- 文字: 主色
- 背景: transparent
- 圓角: 20px
```

### 佈局標準

#### 1. 頁面結構
```
┌─────────────────┐
│   Status Bar    │ ← 系統狀態欄
├─────────────────┤
│     Header      │ ← 56px 頂部導航
├─────────────────┤
│   Search Bar    │ ← 搜索欄 (可選)
├─────────────────┤
│  Category Tabs  │ ← 分類標籤 (可選)
├─────────────────┤
│                 │
│    Content      │ ← 主要內容區域
│                 │
├─────────────────┤
│    Tab Bar      │ ← 70px 底部標籤欄
└─────────────────┘
```

#### 2. 網格系統
- **2列網格**: 商品卡片、課程包卡片
- **4列網格**: 快速操作、功能圖標
- **間距標準**: 16px (外邊距), 12px (內間距)

#### 3. 字體規範
```css
大標題: 18px, font-weight: bold
中標題: 16px, font-weight: bold
小標題: 14px, font-weight: bold
正文: 14px, font-weight: normal
描述: 12px, font-weight: normal
標籤: 10px, font-weight: bold
```

## 🔧 組件使用指南

### 首頁組件
```tsx
import HomeHeader from '../../components/HomeHeader';
import HomeSearchBar from '../../components/HomeSearchBar';

// 使用示例
<HomeHeader onProfile={handleProfile} onNotification={handleNotification} />
<HomeSearchBar placeholder="搜索課程包、教練..." onPress={handleSearch} />
```

### 商城組件
```tsx
import MallHeader from '../../components/MallHeader';
import CategoryTabs from '../../components/CategoryTabs';
import MallProductCard from '../../components/MallProductCard';

// 使用示例
<MallHeader onBack={handleBack} onMenu={handleMenu} />
<CategoryTabs categories={categories} activeCategory={active} onCategoryChange={setActive} />
```

### 教練組件
```tsx
import CoachHeader from '../../components/CoachHeader';
import CoachFilterTabs from '../../components/CoachFilterTabs';

// 使用示例
<CoachHeader onBack={handleBack} onSearch={handleSearch} onFilter={handleFilter} />
<CoachFilterTabs categories={levels} activeCategory={active} onCategoryChange={setActive} />
```

### 個人頁面組件
```tsx
import ProfileHeader from '../../components/ProfileHeader';

// 使用示例
<ProfileHeader onSettings={handleSettings} onNotification={handleNotification} />
```

## 🎯 交互規範

### 1. 點擊反饋
- 所有可點擊元素必須有視覺反饋
- 按鈕點擊時透明度變為 0.8
- 卡片點擊時輕微縮放效果

### 2. 加載狀態
- 使用 RefreshControl 實現下拉刷新
- 加載中顯示 ActivityIndicator
- 空狀態友好提示

### 3. 導航轉場
- 頁面切換使用原生轉場動畫
- 標籤欄切換無轉場效果
- 模態框使用滑入動畫

## 📊 數據展示規範

### 1. 數字格式
- 價格: `¥1,299.00`
- 評分: `4.9`
- 數量: `156人學習`

### 2. 狀態指示
- 在線狀態: 綠色圓點
- 熱銷標識: 紅色標籤
- 新品標識: 橙色標籤

### 3. 徽章設計
- 等級徽章: 對應等級顏色
- 數量徽章: 紅色圓點，右上角位置
- 狀態徽章: 圓角矩形

## 🚀 響應式設計

### 1. 屏幕適配
- 基準寬度: 375px (iPhone X)
- 最小寬度: 320px
- 最大寬度: 414px

### 2. 字體縮放
- 支持系統字體大小設置
- 最小字體: 10px
- 最大字體: 24px

### 3. 圖片適配
- 使用 `resizeMode="cover"`
- 提供多尺寸圖片資源
- 支持高清屏顯示

## 🎨 主題色彩搭配

### 頁面主題色分配原理
1. **首頁綠色** (#5a9178): 代表成長、學習、自然
2. **商城藍色** (#1890ff): 代表信任、專業、科技
3. **教練藍色** (#1890ff): 與商城保持一致，體現專業性
4. **我的紫色** (#722ed1): 代表個性、高端、專屬

### 輔助色使用場景
- **成功綠色**: 完成狀態、在線狀態、成功提示
- **警告橙色**: 待處理狀態、價格強調
- **錯誤紅色**: 錯誤提示、熱銷標籤、緊急狀態
- **收藏粉色**: 收藏功能、女性用戶偏好
- **黃金色彩**: 評分星級、VIP等級、金牌教練

## 📱 底部標籤欄設計

```tsx
標籤配置:
首頁: home | home-filled | #5a9178
商城: shopping-cart | shopping-cart-filled | #1890ff  
教練: person | person-filled | #1890ff
我的: account-circle | account-circle-filled | #722ed1

規格:
- 高度: 70px
- 圖標大小: 24px
- 文字大小: 10px
- 活躍狀態: 對應主色
- 非活躍狀態: #666666
```

## 🔍 最佳實踐

### 1. 性能優化
- 圖片懶加載
- 列表虛擬化
- 合理的緩存策略

### 2. 用戶體驗
- 清晰的視覺層次
- 一致的交互模式
- 友好的錯誤處理

### 3. 可訪問性
- 適當的對比度
- 合理的觸摸目標大小
- 支持屏幕閱讀器

## 📋 組件清單

### 已創建組件
- ✅ HomeHeader.tsx - 首頁頂部導航
- ✅ HomeSearchBar.tsx - 首頁搜索欄
- ✅ MallHeader.tsx - 商城頂部導航
- ✅ CategoryTabs.tsx - 商城分類標籤
- ✅ MallProductCard.tsx - 商城商品卡片
- ✅ CoachHeader.tsx - 教練頁頂部導航
- ✅ CoachFilterTabs.tsx - 教練篩選標籤
- ✅ ProfileHeader.tsx - 個人頁面頂部導航

### 核心頁面
- ✅ HomeScreen.tsx - 首頁
- ✅ MallScreen.tsx - 商城頁
- ✅ CoachListScreen.tsx - 教練列表頁
- ✅ ProfileScreen.tsx - 個人資料頁

### 預覽頁面
- ✅ all-pages-preview.html - 四頁面統一預覽

## 🎯 總結

通過統一的設計標準，SQ台球教學應用實現了：

1. **視覺一致性**: 統一的色彩體系和組件規範
2. **交互一致性**: 相同的操作模式和反饋機制  
3. **功能完整性**: 覆蓋核心業務流程的四大頁面
4. **擴展性**: 可復用的組件和設計模式
5. **用戶體驗**: 直觀易用的界面設計

這套設計標準為後續開發提供了清晰的指導方針，確保整個應用保持專業、統一的視覺體驗。 