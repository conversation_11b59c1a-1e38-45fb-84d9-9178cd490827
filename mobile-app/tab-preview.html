<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shuan-Q Tab 導航預覽</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }

        .phone-container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .phone-header {
            background: #000;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .content-area {
            height: 600px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .content-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        }

        .feature-list {
            list-style: none;
            text-align: left;
        }

        .feature-list li {
            margin: 8px 0;
            color: #555;
            font-size: 14px;
        }

        .feature-list li:before {
            content: '✓ ';
            color: #5a9178;
            font-weight: bold;
        }

        .tab-bar {
            display: flex;
            background: white;
            border-top: 0.5px solid #e5e5e5;
            height: 70px;
            padding: 8px 0;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.05);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 4px;
        }

        .tab-item:hover {
            background: rgba(0,0,0,0.02);
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
            transition: transform 0.2s ease;
        }

        .tab-label {
            font-size: 11px;
            font-weight: 400;
            color: #999999;
            transition: color 0.2s ease;
        }

        .tab-item.active .tab-icon {
            transform: scale(1.1);
        }

        .tab-item.active .tab-label {
            color: #000000;
            font-weight: 500;
        }

        .controls {
            margin-top: 20px;
            text-align: center;
        }

        .tab-button {
            background: #5a9178;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 4px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s ease;
        }

        .tab-button:hover {
            background: #4a7968;
        }

        .tab-button.active {
            background: #333;
        }

        .info-panel {
            margin-top: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .info-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .spec-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
        }

        .spec-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
        }

        .spec-label {
            color: #666;
            font-size: 12px;
        }

        .spec-value {
            color: #333;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-header">
            Shuan-Q 台球教練預約平台
        </div>
        
        <div class="content-area">
            <div class="content-title">Tab 導航預覽</div>
            <div class="content-subtitle">根據您的設計圖片製作</div>
            
            <ul class="feature-list">
                <li>簡潔的黑白配色方案</li>
                <li>激活時圖標放大效果</li>
                <li>實心/輪廓圖標切換</li>
                <li>流暢的狀態切換動畫</li>
                <li>響應式設計適配</li>
            </ul>
        </div>

        <div class="tab-bar">
            <div class="tab-item active" data-tab="home">
                <div class="tab-icon">🏠</div>
                <div class="tab-label">首頁</div>
            </div>
            <div class="tab-item" data-tab="mall">
                <div class="tab-icon">🛒</div>
                <div class="tab-label">商城</div>
            </div>
            <div class="tab-item" data-tab="coach">
                <div class="tab-icon">👨‍🏫</div>
                <div class="tab-label">教練</div>
            </div>
            <div class="tab-item" data-tab="profile">
                <div class="tab-icon">😊</div>
                <div class="tab-label">我的</div>
            </div>
        </div>
    </div>

    <div class="controls">
        <button class="tab-button active" onclick="switchTab('home')">首頁</button>
        <button class="tab-button" onclick="switchTab('mall')">商城</button>
        <button class="tab-button" onclick="switchTab('coach')">教練</button>
        <button class="tab-button" onclick="switchTab('profile')">我的</button>
    </div>

    <div class="info-panel">
        <div class="info-title">設計規格</div>
        <div class="spec-grid">
            <div class="spec-item">
                <div class="spec-label">導航欄高度</div>
                <div class="spec-value">70px</div>
            </div>
            <div class="spec-item">
                <div class="spec-label">圖標大小</div>
                <div class="spec-value">24-26px</div>
            </div>
            <div class="spec-item">
                <div class="spec-label">激活顏色</div>
                <div class="spec-value">#000000</div>
            </div>
            <div class="spec-item">
                <div class="spec-label">非激活顏色</div>
                <div class="spec-value">#999999</div>
            </div>
            <div class="spec-item">
                <div class="spec-label">字體大小</div>
                <div class="spec-value">11px</div>
            </div>
            <div class="spec-item">
                <div class="spec-label">陰影效果</div>
                <div class="spec-value">0 -2px 8px</div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 移除所有活動狀態
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加新的活動狀態
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            event.target.classList.add('active');

            // 更新內容
            const titles = {
                home: '首頁內容',
                mall: '商城內容', 
                coach: '教練內容',
                profile: '我的內容'
            };

            document.querySelector('.content-title').textContent = titles[tabName];
            document.querySelector('.content-subtitle').textContent = `${tabName} Tab 已激活`;
        }

        // 自動演示
        let currentIndex = 0;
        const tabs = ['home', 'mall', 'coach', 'profile'];
        
        setInterval(() => {
            currentIndex = (currentIndex + 1) % tabs.length;
            const tabName = tabs[currentIndex];
            
            // 模擬點擊
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            const titles = {
                home: '首頁內容',
                mall: '商城內容', 
                coach: '教練內容',
                profile: '我的內容'
            };
            
            document.querySelector('.content-title').textContent = titles[tabName];
        }, 3000);
    </script>
</body>
</html> 