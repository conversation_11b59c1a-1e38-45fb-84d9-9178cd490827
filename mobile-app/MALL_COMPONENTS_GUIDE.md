# SQ商城頁面 - 組件架構指南

## 概述

基於您提供的SQ商城截圖，我們已經分析並實現了商城頁面的核心UI組件架構。本文檔詳細說明了每個組件的設計理念、功能特性和實現方式。

## 頁面架構分析

### 1. 整體布局結構
```
SafeAreaView (容器)
├── MallHeader (頂部導航欄)
├── MallSearchBar (搜索欄)
├── CategoryTabs (分類標籤)
├── FlatList (商品網格列表)
│   └── MallProductCard (商品卡片)
└── Bottom Tab Navigation (底部導航)
```

## 核心組件詳解

### 🔝 MallHeader - 頂部導航欄
**文件位置**: `src/components/MallHeader.tsx`

**設計特點**:
- 左側返回按鈕 (arrow-back)
- 中央標題 "SQ商城"
- 右側功能按鈕組 (更多菜單 ⋯ + 錄製 📹)
- 白色背景，陰影效果

**技術實現**:
```typescript
interface MallHeaderProps {
  onBack?: () => void;
  onMenu?: () => void;
  onRecord?: () => void;
}
```

**關鍵功能**:
- 統一的導航體驗
- 可擴展的右側功能區
- Material Design圖標集成

### 🔍 MallSearchBar - 搜索欄
**文件位置**: `src/components/MallSearchBar.tsx`

**設計特點**:
- 圓角搜索框設計
- 搜索圖標 + "搜索商品、教練" 提示文字
- 支持可編輯/不可編輯模式
- 灰色背景區域

**技術實現**:
```typescript
interface MallSearchBarProps {
  placeholder?: string;
  onPress?: () => void;
  onSearch?: (query: string) => void;
  editable?: boolean;
  value?: string;
}
```

**關鍵功能**:
- 雙模式支持（點擊跳轉 vs 直接輸入）
- 自定義佔位符文字
- 搜索事件處理

### 🏷️ CategoryTabs - 分類標籤
**文件位置**: `src/components/CategoryTabs.tsx`

**設計特點**:
- 水平滾動標籤列表
- 選中狀態：藍色文字 (#1890ff) + 下劃線
- 未選中：灰色文字 (#666)
- 標籤：品牌、品類、銷量、價格

**技術實現**:
```typescript
interface CategoryTabsProps {
  categories: Category[];
  selectedCategory: string;
  onCategorySelect: (categoryId: string) => void;
}
```

**關鍵功能**:
- 動態下劃線動畫
- 水平滾動支持
- 狀態切換管理

### 🛍️ MallProductCard - 商品卡片
**文件位置**: `src/components/MallProductCard.tsx`

**設計特點**:
- 2列網格布局
- 商品圖片區域 (150px高度)
- 熱銷標籤 (紅色背景 #ff4757)
- 收藏按鈕 (心形圖標)
- 商品信息：名稱、價格、原價、銷量

**技術實現**:
```typescript
interface MallProductCardProps {
  product: Product;
  onPress: () => void;
  onFavorite?: (productId: string, isFavorite: boolean) => void;
}
```

**關鍵功能**:
- 圖片懒加載支持
- 收藏狀態切換
- 價格對比顯示
- 熱銷標籤條件渲染

### 📱 MallScreen - 主頁面
**文件位置**: `src/screens/mall/MallScreen.tsx`

**設計特點**:
- SafeAreaView 容器
- 下拉刷新支持
- 分類篩選功能
- 商品網格展示

**技術實現**:
```typescript
const MallScreen: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('brand');
  const [refreshing, setRefreshing] = useState(false);
  // ...
}
```

## 數據結構設計

### 商品數據模型
```typescript
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  sales?: number;
  isHot?: boolean;
}
```

### 分類數據模型
```typescript
interface Category {
  id: string;
  name: string;
}
```

## 視覺設計規範

### 🎨 色彩系統
- **主要藍色**: #1890ff (選中狀態)
- **紅色強調**: #ff4757 (價格、熱銷標籤)
- **文字灰色**: #666 (次要文字)
- **背景灰色**: #f8f9fa (頁面背景)
- **邊框灰色**: #f0f0f0 (分隔線)

### 📐 尺寸規範
- **卡片間距**: 16px
- **內容邊距**: 12px
- **圓角半徑**: 8px (卡片), 25px (搜索框)
- **標籤高度**: 56px (導航), 70px (底部Tab)

### 🔤 字體規範
- **標題字體**: 18px, bold
- **商品名稱**: 14px, regular
- **價格**: 18px, bold
- **次要信息**: 12px, regular

## 交互邏輯

### 分類切換流程
1. 用戶點擊分類標籤
2. 更新選中狀態視覺效果
3. 觸發商品列表重新加載
4. 保持滾動位置在頂部

### 商品交互流程
1. **點擊商品卡片** → 跳轉商品詳情頁
2. **點擊收藏按鈕** → 切換收藏狀態
3. **下拉頁面** → 刷新商品列表

### 搜索交互流程
1. 點擊搜索欄 → 跳轉搜索頁面
2. 傳遞當前分類信息
3. 支持商品和教練搜索

## 性能優化策略

### 圖片加載優化
- 使用 `defaultSource` 提供預設圖片
- 實現圖片懶加載機制
- 支持圖片緩存策略

### 列表渲染優化
- FlatList 虛擬化滾動
- `keyExtractor` 優化key生成
- `getItemLayout` 固定項目高度

### 狀態管理優化
- Redux/Context 狀態集中管理
- 分類狀態本地化管理
- 防抖搜索輸入處理

## 擴展功能建議

### 短期優化
1. **商品篩選器** - 價格區間、品牌篩選
2. **排序功能** - 價格、銷量、評分排序
3. **商品收藏** - 本地收藏夾功能
4. **購物車** - 快速加入購物車

### 長期擴展
1. **商品推薦** - 個性化推薦算法
2. **AR試用** - 商品虛擬試用功能
3. **直播購物** - 教練直播帶貨
4. **社群功能** - 商品評論與分享

## 部署與預覽

### 瀏覽器預覽
運行以下命令打開HTML預覽：
```bash
cd mobile-app
open mall-screen-preview.html
```

### React Native 運行
```bash
cd mobile-app
npm install
npx react-native run-ios  # iOS
npx react-native run-android  # Android
```

## 總結

基於截圖分析，我們成功實現了：

✅ **完整的UI組件體系** - 所有核心組件模組化開發
✅ **一致的視覺設計** - 符合原始設計規範
✅ **良好的交互體驗** - 支持觸摸、滾動、狀態切換
✅ **可擴展的架構** - 便於後續功能迭代
✅ **響應式設計** - 適配不同屏幕尺寸

該商城頁面架構為SQ平台提供了堅實的電商功能基礎，可以支撐後續的業務發展需求。 