<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>-<PERSON> 扁平化圖標預覽</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .phone-container {
            max-width: 375px;
            margin: 0 auto 40px;
            background: white;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .phone-header {
            background: #000;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            position: relative;
        }

        .phone-header::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: #333;
            border-radius: 2px;
        }

        .content-area {
            height: 600px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            position: relative;
        }

        .content-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .content-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            max-width: 280px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .feature-text {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .tab-bar {
            display: flex;
            background: white;
            border-top: 0.5px solid #e5e5e5;
            height: 70px;
            padding: 8px 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px;
            position: relative;
        }

        .tab-item:hover {
            background: rgba(0,0,0,0.02);
        }

        .tab-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-label {
            font-size: 11px;
            font-weight: 400;
            color: #999999;
            transition: all 0.3s ease;
        }

        .tab-item.active .tab-icon {
            transform: scale(1.15);
        }

        .tab-item.active .tab-label {
            color: #000000;
            font-weight: 600;
        }

        /* 扁平化 SVG 圖標樣式 */
        .home-icon {
            fill: #999999;
            transition: fill 0.3s ease;
        }

        .mall-icon {
            fill: #999999;
            transition: fill 0.3s ease;
        }

        .coach-icon {
            fill: #999999;
            transition: fill 0.3s ease;
        }

        .profile-icon {
            fill: #999999;
            transition: fill 0.3s ease;
        }

        .tab-item.active .home-icon,
        .tab-item.active .mall-icon,
        .tab-item.active .coach-icon,
        .tab-item.active .profile-icon {
            fill: #000000;
        }

        .icon-showcase {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .showcase-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 30px;
        }

        .icon-demo {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .icon-demo:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .icon-demo svg {
            margin-bottom: 12px;
        }

        .icon-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .icon-desc {
            font-size: 12px;
            color: #666;
        }

        .design-principles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .principle-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .principle-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .principle-title::before {
            content: '●';
            color: #5a9178;
            margin-right: 10px;
            font-size: 20px;
        }

        .principle-list {
            list-style: none;
        }

        .principle-list li {
            margin: 8px 0;
            color: #666;
            font-size: 14px;
            padding-left: 15px;
            position: relative;
        }

        .principle-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #5a9178;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎱 Shuan-Q 扁平化圖標設計</h1>
            <p>簡潔 · 現代 · 一致的視覺體驗</p>
        </div>

        <div class="phone-container">
            <div class="phone-header">
                Shuan-Q 台球教練預約平台
            </div>
            
            <div class="content-area">
                <div class="content-title">扁平化設計</div>
                <div class="content-subtitle">Tab 圖標優化完成</div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-text">扁平化設計</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-text">SVG 向量圖標</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <div class="feature-text">響應式適配</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <div class="feature-text">流暢動畫</div>
                    </div>
                </div>
            </div>

            <div class="tab-bar">
                <div class="tab-item active" data-tab="home">
                    <div class="tab-icon">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path class="home-icon" d="M12 3L20 9V21H15V16H9V21H4V9L12 3Z"/>
                        </svg>
                    </div>
                    <div class="tab-label">首頁</div>
                </div>
                <div class="tab-item" data-tab="mall">
                    <div class="tab-icon">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path class="mall-icon" d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
                            <circle class="mall-icon" cx="10" cy="12" r="1"/>
                            <circle class="mall-icon" cx="14" cy="12" r="1"/>
                        </svg>
                    </div>
                    <div class="tab-label">商城</div>
                </div>
                <div class="tab-item" data-tab="coach">
                    <div class="tab-icon">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <circle class="coach-icon" cx="12" cy="8" r="4"/>
                            <path class="coach-icon" d="M12 14C8 14 4 16 4 18V20H20V18C20 16 16 14 12 14Z"/>
                        </svg>
                    </div>
                    <div class="tab-label">教練</div>
                </div>
                <div class="tab-item" data-tab="profile">
                    <div class="tab-icon">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <circle class="profile-icon" cx="12" cy="12" r="10"/>
                            <circle fill="white" cx="9" cy="10" r="1.5"/>
                            <circle fill="white" cx="15" cy="10" r="1.5"/>
                            <path stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none" d="M8 15C8.5 16 10 17 12 17C14 17 15.5 16 16 15"/>
                        </svg>
                    </div>
                    <div class="tab-label">我的</div>
                </div>
            </div>
        </div>

        <div class="icon-showcase">
            <div class="showcase-title">扁平化圖標展示</div>
            
            <div class="icon-grid">
                <div class="icon-demo">
                    <svg viewBox="0 0 24 24" width="48" height="48">
                        <path fill="#333" d="M12 3L20 9V21H15V16H9V21H4V9L12 3Z"/>
                    </svg>
                    <div class="icon-name">首頁</div>
                    <div class="icon-desc">簡潔的房屋造型</div>
                </div>
                
                <div class="icon-demo">
                    <svg viewBox="0 0 24 24" width="48" height="48">
                        <path fill="#333" d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
                        <circle fill="#333" cx="10" cy="12" r="1"/>
                        <circle fill="#333" cx="14" cy="12" r="1"/>
                    </svg>
                    <div class="icon-name">商城</div>
                    <div class="icon-desc">購物袋設計</div>
                </div>
                
                <div class="icon-demo">
                    <svg viewBox="0 0 24 24" width="48" height="48">
                        <circle fill="#333" cx="12" cy="8" r="4"/>
                        <path fill="#333" d="M12 14C8 14 4 16 4 18V20H20V18C20 16 16 14 12 14Z"/>
                    </svg>
                    <div class="icon-name">教練</div>
                    <div class="icon-desc">人物輪廓設計</div>
                </div>
                
                <div class="icon-demo">
                    <svg viewBox="0 0 24 24" width="48" height="48">
                        <circle fill="#333" cx="12" cy="12" r="10"/>
                        <circle fill="white" cx="9" cy="10" r="1.5"/>
                        <circle fill="white" cx="15" cy="10" r="1.5"/>
                        <path stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none" d="M8 15C8.5 16 10 17 12 17C14 17 15.5 16 16 15"/>
                    </svg>
                    <div class="icon-name">我的</div>
                    <div class="icon-desc">友好的笑臉</div>
                </div>
            </div>
        </div>

        <div class="design-principles">
            <div class="principle-card">
                <div class="principle-title">扁平化原則</div>
                <ul class="principle-list">
                    <li>去除不必要的裝飾元素</li>
                    <li>使用純色填充</li>
                    <li>保持簡潔的線條</li>
                    <li>統一的視覺風格</li>
                </ul>
            </div>
            
            <div class="principle-card">
                <div class="principle-title">技術優勢</div>
                <ul class="principle-list">
                    <li>SVG 向量圖標，無損縮放</li>
                    <li>更小的文件尺寸</li>
                    <li>更好的性能表現</li>
                    <li>支持動態顏色切換</li>
                </ul>
            </div>
            
            <div class="principle-card">
                <div class="principle-title">用戶體驗</div>
                <ul class="principle-list">
                    <li>清晰的視覺層次</li>
                    <li>一致的交互反饋</li>
                    <li>現代化的設計語言</li>
                    <li>良好的可讀性</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            const titles = {
                home: '首頁內容',
                mall: '商城內容', 
                coach: '教練內容',
                profile: '我的內容'
            };
            
            document.querySelector('.content-title').textContent = titles[tabName];
            document.querySelector('.content-subtitle').textContent = `${tabName} Tab 已激活`;
        }

        // 為 Tab 項目添加點擊事件
        document.querySelectorAll('.tab-item').forEach(item => {
            item.addEventListener('click', () => {
                const tabName = item.getAttribute('data-tab');
                switchTab(tabName);
            });
        });

        // 自動演示
        let currentIndex = 0;
        const tabs = ['home', 'mall', 'coach', 'profile'];
        
        setInterval(() => {
            currentIndex = (currentIndex + 1) % tabs.length;
            const tabName = tabs[currentIndex];
            switchTab(tabName);
        }, 4000);
    </script>
</body>
</html> 