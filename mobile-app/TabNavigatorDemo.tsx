import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import IonIcon from 'react-native-vector-icons/Ionicons';

const { width } = Dimensions.get('window');

interface TabItem {
  name: string;
  label: string;
  icon: string;
  iconFamily: 'MaterialIcons' | 'Ionicons';
}

const tabs: TabItem[] = [
  { name: 'Home', label: '首页', icon: 'home', iconFamily: 'MaterialIcons' },
  { name: 'Mall', label: '商城', icon: 'storefront', iconFamily: 'Ionicons' },
  { name: 'Coach', label: '教练', icon: 'person', iconFamily: 'Ionicons' },
  { name: 'Profile', label: '我的', icon: 'happy', iconFamily: 'Ionicons' },
];

const TabNavigatorDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Home');

  const renderIcon = (tab: TabItem, isActive: boolean) => {
    const iconColor = isActive ? '#000000' : '#999999';
    const iconSize = isActive ? 26 : 24;

    if (tab.iconFamily === 'MaterialIcons') {
      return <Icon name={tab.icon} size={iconSize} color={iconColor} />;
    } else {
      const iconName = isActive ? tab.icon : `${tab.icon}-outline`;
      return <IonIcon name={iconName} size={iconSize} color={iconColor} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Shuan-Q 台球教練預約平台</Text>
        <Text style={styles.subtitle}>新版 Tab 導航設計</Text>
      </View>

      {/* Tab Navigation Bar */}
      <View style={styles.tabBar}>
        {tabs.map((tab) => {
          const isActive = activeTab === tab.name;
          return (
            <TouchableOpacity
              key={tab.name}
              style={styles.tabItem}
              onPress={() => setActiveTab(tab.name)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, isActive && styles.activeIconContainer]}>
                {renderIcon(tab, isActive)}
              </View>
              <Text
                style={[
                  styles.tabLabel,
                  isActive ? styles.activeTabLabel : styles.inactiveTabLabel,
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderTopWidth: 0.5,
    borderTopColor: '#e5e5e5',
    paddingBottom: 8,
    paddingTop: 8,
    height: 70,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
    marginBottom: 2,
  },
  activeIconContainer: {
    transform: [{ scale: 1.1 }],
  },
  tabLabel: {
    fontSize: 11,
    fontWeight: '400',
    textAlign: 'center',
  },
  activeTabLabel: {
    color: '#000000',
    fontWeight: '500',
  },
  inactiveTabLabel: {
    color: '#999999',
  },
});

export default TabNavigatorDemo; 