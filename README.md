# 🎱 Shuan-Q 台球教練預約平台

## 📋 專案概述

**Shuan-Q** 是一個專業的台球教練預約平台，採用現代化的技術架構，為學員和教練提供便捷的台球教學服務。

## 🏗️ 專案架構

```
Shuan-Q/
├── backend/                    # 後端 API 服務
│   ├── src/
│   │   ├── models/            # 數據模型
│   │   ├── controllers/       # 控制器
│   │   ├── routes/           # 路由配置
│   │   ├── middleware/       # 中間件
│   │   └── utils/            # 工具函數
│   └── package.json          # 後端依賴
├── mobile-app/                # React Native 移動應用
│   ├── src/
│   │   ├── components/       # 通用組件
│   │   ├── screens/         # 頁面組件
│   │   ├── navigation/      # 導航配置
│   │   ├── store/           # 狀態管理
│   │   └── services/        # API 服務
│   └── package.json         # 前端依賴
├── shared/                   # 共享代碼庫
│   ├── types/               # TypeScript 類型定義
│   └── constants/           # 常量定義
└── docs/                    # 專案文檔
    ├── architecture/        # 架構文檔
    ├── guides/             # 使用指南
    └── development/        # 開發文檔
```

## 🚀 快速開始

### 環境要求
- Node.js 16+
- React Native CLI
- iOS/Android 開發環境

### 安裝與運行

#### 1. 後端服務
```bash
cd backend
npm install
npm run dev
```

#### 2. 移動應用
```bash
cd mobile-app
npm install
npm start
```

## 📱 核心功能

### 👨‍🎓 學員功能
- ✅ 手機號驗證碼登錄
- ✅ 瀏覽教練信息
- ✅ 搜索和篩選教練
- ✅ 預約教練課程
- ✅ 購買課程包
- ✅ 管理訂單和預約

### 👨‍🏫 教練功能
- ✅ 完善教練資料
- ✅ 創建和管理課程包
- ✅ 查看和管理預約
- ✅ 學員管理
- ✅ 收入統計

## 🛠️ 技術棧

### 後端
- **Node.js + Express** - 服務器框架
- **Sequelize + SQLite** - 數據庫 ORM
- **JWT** - 身份認證
- **Express Validator** - 參數驗證

### 前端
- **React Native** - 跨平台移動開發
- **TypeScript** - 類型安全
- **Redux Toolkit** - 狀態管理
- **React Navigation** - 路由導航

## 📖 文檔

- [架構文檔](./docs/architecture/) - 項目架構和設計文檔
- [開發指南](./docs/guides/) - 開發和配置指南
- [開發記錄](./docs/development/) - 開發過程和版本記錄

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改進這個項目。

## 📄 許可證

MIT License

---

**注意**: 此項目專注於移動端開發，微信小程序版本將在移動端完成後開發。 