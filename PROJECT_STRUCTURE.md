# 📁 Shuan-Q 項目結構說明

## 🎯 整理完成

項目已成功整理，清理了微信小程序相關文件，專注於移動端開發。

## 📂 目錄結構

```
Shuan-Q/
├── README.md                   # 項目主要說明文件
├── PROJECT_STRUCTURE.md        # 本文件 - 項目結構說明
├── .gitignore                  # Git 忽略配置
│
├── backend/                    # 🚀 後端 API 服務
│   ├── src/
│   │   ├── config/            # 數據庫配置
│   │   ├── models/            # 數據模型 (User, Coach, Appointment 等)
│   │   ├── controllers/       # 業務邏輯控制器
│   │   ├── routes/           # API 路由配置
│   │   ├── middleware/       # 認證和驗證中間件
│   │   ├── utils/            # JWT、SMS 等工具函數
│   │   └── index.js          # 應用入口
│   ├── package.json          # 後端依賴配置
│   └── tsconfig.json         # TypeScript 配置
│
├── mobile-app/                # 📱 React Native 移動應用
│   ├── src/
│   │   ├── components/       # 通用 UI 組件
│   │   │   ├── Banner.tsx
│   │   │   ├── CoachCard.tsx
│   │   │   ├── ProductCard.tsx
│   │   │   └── ...
│   │   ├── screens/         # 頁面組件
│   │   │   ├── home/       # 首頁模塊
│   │   │   ├── coach/      # 教練模塊
│   │   │   ├── profile/    # 個人中心
│   │   │   └── ...
│   │   ├── navigation/     # 導航配置
│   │   ├── store/         # Redux 狀態管理
│   │   ├── services/      # API 集成服務
│   │   └── utils/         # 工具函數
│   ├── App.tsx            # 應用入口
│   ├── package.json       # 前端依賴配置
│   └── tsconfig.json      # TypeScript 配置
│
├── shared/                    # 🔗 共享代碼庫
│   ├── types/                # TypeScript 類型定義
│   │   ├── User.ts
│   │   ├── Coach.ts
│   │   ├── Appointment.ts
│   │   └── ...
│   ├── constants/           # 常量定義
│   │   ├── api.ts
│   │   ├── roles.ts
│   │   └── index.ts
│   └── package.json        # 共享庫配置
│
└── docs/                     # 📚 項目文檔
    ├── architecture/        # 架構設計文檔
    │   ├── MOBILE_APP_ARCHITECTURE_SUMMARY.md
    │   ├── MOBILE_APP_QUICK_START.md
    │   └── COMPONENT_USAGE_GUIDE.md
    ├── guides/             # 使用和配置指南
    │   ├── API_INTEGRATION_GUIDE.md
    │   └── ...
    ├── development/        # 開發記錄和總結
    │   ├── PROJECT_FINAL_SUMMARY.md
    │   ├── PHASE2_COMPLETION.md
    │   ├── PHASE3_COMPLETION.md
    │   └── ...
    ├── api/               # API 文檔
    ├── design/            # 設計文檔
    └── deployment.md      # 部署說明
```

## 🗑️ 已清理的內容

### 已刪除的微信小程序相關文件
- ❌ `miniprogram/` - 整個微信小程序目錄
- ❌ `project.config.json` - 微信小程序配置
- ❌ `project.private.config.json` - 微信小程序私有配置

### 已刪除的演示和測試文件
- ❌ `mobile-demo/` - 移動端演示目錄
- ❌ `web-demo/` - Web 演示目錄
- ❌ `mobile-showcase.html` - 移動端展示頁面
- ❌ `demo.js` - 演示腳本
- ❌ `start-demo.sh` - 演示啟動腳本

### 已刪除的診斷和配置文件
- ❌ `diagnostic-script.*` - 診斷腳本
- ❌ `fix-environment.sh` - 環境修復腳本
- ❌ `development-issues-analysis.md` - 開發問題分析
- ❌ `mcp-config-*.json` - MCP 配置文件
- ❌ `augment-settings*.json` - Augment 設置文件
- ❌ 根目錄的 `node_modules/` 和 `package.json` - 移至子項目管理

### 已刪除的 MCP 相關文件
- ❌ `MCP-FEEDBACK-ENHANCED-安装指南.md` - MCP 反饋增強安裝指南
- ❌ `AUGMENT-MCP-配置指南.md` - Augment MCP 配置指南
- ❌ `MCP-QUICK-REFERENCE.md` - MCP 快速參考
- ❌ `SHUAN-Q-MCP-集成指南.md` - Shuan-Q MCP 集成指南

## 📋 整理成果

### ✅ 結構清晰
- 核心代碼組織在 `backend/`、`mobile-app/`、`shared/` 三個主要目錄
- 文檔按類型分類在 `docs/` 目錄下
- 每個子項目有獨立的依賴管理

### ✅ 專注移動端
- 清理了微信小程序相關文件
- 保留了 React Native 移動應用的完整架構
- 為後續小程序開發預留了空間

### ✅ 文檔組織
- **架構文檔**: 技術架構和設計說明
- **使用指南**: 開發和配置指南
- **開發記錄**: 項目進展和版本記錄

## 🚀 下一步開發建議

1. **專注移動端 UI 設計**
   - 完善 React Native 組件庫
   - 優化用戶界面和體驗
   - 實現響應式設計

2. **功能完善**
   - 完成核心業務流程
   - 添加錯誤處理和邊界情況
   - 性能優化和測試

3. **後續小程序開發**
   - 在移動端穩定後
   - 基於現有後端 API
   - 復用 shared/ 中的類型定義

## 📞 聯繫支持

如需要進一步的結構調整或功能開發，請提供具體需求。 