<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHUAN-Q 融資計劃書 - 台球教練預約平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }

        .presentation {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            padding: 40px 60px 120px 60px; /* 增加底部padding為導航按鈕留空間 */
            display: none;
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
            overflow-y: auto; /* 允許垂直滾動 */
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide.white-bg {
            background: #fff;
            color: #000;
        }

        .slide-header {
            position: absolute;
            top: 20px;
            right: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: #1B5E20;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }

        .page-number {
            color: #1B5E20;
            font-size: 14px;
            font-weight: 500;
        }

        .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 40px;
            text-align: center;
            color: #1B5E20;
        }

        .slide-subtitle {
            font-size: 24px;
            margin-bottom: 60px;
            text-align: center;
            color: #1B5E20;
        }

        .main-logo {
            font-size: 56px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #fff;
            text-align: center;
        }

        .billiards-subtitle {
            font-size: 24px;
            color: #1B5E20;
            margin-bottom: 40px;
            letter-spacing: 2px;
        }

        .funding-info {
            font-size: 20px;
            color: #fff;
            opacity: 0.8;
        }

        .highlight-box {
            background: rgba(27, 94, 32, 0.1);
            border: 2px solid #1B5E20;
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            flex: 1;
            text-align: center;
        }

        .highlight-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            width: 100%;
            max-width: 1200px;
        }

        .highlight-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .highlight-title {
            font-size: 24px;
            font-weight: bold;
            color: #1B5E20;
            margin-bottom: 10px;
        }

        .highlight-content {
            font-size: 18px;
            line-height: 1.5;
            color: #333;
        }

        .big-number {
            font-size: 56px;
            font-weight: bold;
            color: #1B5E20;
            margin: 20px 0;
        }

        .investment-overview {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .key-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            margin: 40px 0;
            width: 100%;
            max-width: 800px;
        }

        .metric-box {
            background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            color: white;
        }

        .metric-number {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 18px;
            opacity: 0.9;
        }

        .timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1000px;
            margin: 40px 0;
        }

        .timeline-item {
            text-align: center;
            flex: 1;
            position: relative;
        }

        .timeline-year {
            font-size: 24px;
            font-weight: bold;
            color: #1B5E20;
            margin-bottom: 10px;
        }

        .timeline-stage {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }

        .timeline-desc {
            font-size: 14px;
            color: #888;
        }

        .arrow {
            position: absolute;
            top: 50%;
            right: -20px;
            transform: translateY(-50%);
            color: #1B5E20;
            font-size: 24px;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 9999; /* 提高z-index確保始終在最上層 */
            background: rgba(0, 0, 0, 0.8); /* 添加半透明背景 */
            padding: 10px 20px;
            border-radius: 50px;
            backdrop-filter: blur(10px); /* 添加毛玻璃效果 */
        }

        .nav-btn {
            background: #1B5E20;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2E7D32;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(27, 94, 32, 0.9); /* 增加不透明度 */
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            color: white; /* 改為白色文字更清晰 */
            z-index: 9999; /* 提高z-index */
            backdrop-filter: blur(10px); /* 添加毛玻璃效果 */
        }

        .pain-points {
            width: 100%;
            max-width: 1000px;
        }

        .pain-section {
            background: rgba(255, 255, 255, 0.05);
            border-left: 5px solid #1B5E20;
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .pain-title {
            font-size: 24px;
            font-weight: bold;
            color: #1B5E20;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pain-list {
            list-style: none;
            padding-left: 20px;
        }

        .pain-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #ccc;
        }

        .pain-list li::before {
            content: "•";
            color: #1B5E20;
            font-size: 20px;
            position: absolute;
            left: 0;
        }

        .market-pyramid {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin: 40px 0;
        }

        .pyramid-level {
            background: linear-gradient(135deg, #1B5E20, #2E7D32);
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            text-align: center;
            position: relative;
        }

        .pyramid-level.level-1 { width: 400px; }
        .pyramid-level.level-2 { width: 320px; }
        .pyramid-level.level-3 { width: 240px; }

        .pyramid-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .pyramid-target {
            font-size: 14px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 20px 30px;
            }
            
            .slide-title {
                font-size: 36px;
            }
            
            .main-logo {
                font-size: 42px;
            }
            
            .highlight-grid {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- Slide 1: Cover -->
        <div class="slide active">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第1頁/共14頁</div>
            </div>
            <div class="main-logo">SHUAN-Q</div>
            <div class="billiards-subtitle">BILLIARDS COACHING</div>
            <div style="font-size: 48px; font-weight: bold; color: #fff; margin: 30px 0;">融資計劃書</div>
            <div style="font-size: 24px; color: #1B5E20; margin-bottom: 40px;">台球教練預約平台</div>
            <div class="funding-info">2024年A輪融資 - 350萬人民幣</div>
            <div style="position: absolute; bottom: 40px; right: 60px; opacity: 0.3;">
                <div style="font-size: 120px; color: #1B5E20;">🎱</div>
            </div>
        </div>

        <!-- Slide 2: Core Highlights -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第2頁/共14頁</div>
            </div>
            <div class="slide-title">🎯 為什麼投資SHUAN-Q？</div>
            <div class="highlight-grid">
                <div class="highlight-box">
                    <div class="highlight-icon">🏆</div>
                    <div class="highlight-title">萬億市場機會</div>
                    <div class="highlight-content">
                        <div class="big-number">8000億</div>
                        技能教培市場<br>
                        數位化程度極低
                    </div>
                </div>
                <div class="highlight-box">
                    <div class="highlight-icon">💡</div>
                    <div class="highlight-title">創新商業模式</div>
                    <div class="highlight-content">
                        教練+商城雙引擎<br>
                        多收入來源<br>
                        業務閉環設計
                    </div>
                </div>
                <div class="highlight-box">
                    <div class="highlight-icon">🚀</div>
                    <div class="highlight-title">完整產品方案</div>
                    <div class="highlight-content">
                        技術實力已驗證<br>
                        跨平台移動應用<br>
                        核心功能完成90%
                    </div>
                </div>
                <div class="highlight-box">
                    <div class="highlight-icon">💰</div>
                    <div class="highlight-title">清晰盈利路徑</div>
                    <div class="highlight-content">
                        輕資產高毛利<br>
                        3年內實現盈利<br>
                        多元化收入結構
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Investment Overview -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第3頁/共14頁</div>
            </div>
            <div class="slide-title">💎 投資機會概覽</div>
            <div class="investment-overview">
                <div style="text-align: center; margin-bottom: 40px;">
                    <div style="font-size: 24px; color: #666; margin-bottom: 10px;">融資金額</div>
                    <div class="big-number" style="font-size: 72px;">350萬</div>
                    <div style="font-size: 24px; color: #666;">人民幣</div>
                </div>
                <div class="key-metrics">
                    <div class="metric-box">
                        <div class="metric-number">19%</div>
                        <div class="metric-label">股權釋出</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-number">270%</div>
                        <div class="metric-label">投資回報率</div>
                    </div>
                </div>
                <div style="text-align: center; margin: 30px 0;">
                    <div style="font-size: 20px; color: #666;">當前估值: 1500萬 → 融資後: 1850萬 → 3年預期: 5000萬</div>
                </div>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-year">2024</div>
                        <div class="timeline-stage">起步期</div>
                        <div class="timeline-desc">台球垂直</div>
                        <div class="arrow">→</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-year">2025</div>
                        <div class="timeline-stage">發展期</div>
                        <div class="timeline-desc">功能完善</div>
                        <div class="arrow">→</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-year">2026</div>
                        <div class="timeline-stage">成長期</div>
                        <div class="timeline-desc">市場擴張</div>
                        <div class="arrow">→</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-year">2027</div>
                        <div class="timeline-stage">盈利期</div>
                        <div class="timeline-desc">多元延展</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Market Pain Points -->
        <div class="slide">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第4頁/共14頁</div>
            </div>
            <div class="slide-title" style="color: #1B5E20;">🎯 市場痛點：機會就在這裡</div>
            <div class="pain-points">
                <div class="pain-section">
                    <div class="pain-title">
                        🔍 學員痛點
                    </div>
                    <ul class="pain-list">
                        <li><strong>找教練難</strong>：優質教練分散，信息不透明</li>
                        <li><strong>價格不明</strong>：沒有標準，容易被坑</li>
                        <li><strong>預約繁瑣</strong>：電話約課，時間衝突多</li>
                    </ul>
                </div>
                <div class="pain-section">
                    <div class="pain-title">
                        👨‍🏫 教練痛點
                    </div>
                    <ul class="pain-list">
                        <li><strong>獲客成本高</strong>：依賴口碑，覆蓋面有限</li>
                        <li><strong>收入不穩定</strong>：淡旺季明顯，現金流差</li>
                        <li><strong>缺乏展示平台</strong>：民間高手無處發光</li>
                    </ul>
                </div>
                <div class="pain-section">
                    <div class="pain-title">
                        🏪 商家痛點
                    </div>
                    <ul class="pain-list">
                        <li><strong>銷售渠道單一</strong>：主要靠實體店</li>
                        <li><strong>獲客成本高</strong>：缺乏精準流量</li>
                        <li><strong>庫存壓力大</strong>：資金占用嚴重</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 5: Market Size -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第5頁/共14頁</div>
            </div>
            <div class="slide-title">📊 萬億市場，藍海機會</div>
            <div class="market-pyramid">
                <div class="pyramid-level level-1">
                    <div class="pyramid-amount">8000億人民幣</div>
                    <div>全技能教培市場</div>
                    <div class="pyramid-target">↗️ 10年目標：1%滲透率 ↖️</div>
                </div>
                <div class="pyramid-level level-2">
                    <div class="pyramid-amount">2000億人民幣</div>
                    <div>體育技能教培市場</div>
                    <div class="pyramid-target">↗️ 5年目標：2%滲透率 ↖️</div>
                </div>
                <div class="pyramid-level level-3">
                    <div class="pyramid-amount">50億人民幣</div>
                    <div>台球教培市場</div>
                    <div class="pyramid-target">↗️ 3年目標：5%滲透率 ↖️</div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 30px; margin-top: 40px; max-width: 1000px;">
                <div style="text-align: center;">
                    <div style="font-size: 32px; font-weight: bold; color: #1B5E20;">6000萬</div>
                    <div style="color: #666;">台球愛好者</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 32px; font-weight: bold; color: #1B5E20;">2000萬</div>
                    <div style="color: #666;">活躍用戶</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 32px; font-weight: bold; color: #1B5E20;">15%</div>
                    <div style="color: #666;">年增長率</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 32px; font-weight: bold; color: #1B5E20;">僅3%</div>
                    <div style="color: #666;">數位化滲透率</div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Competition Analysis -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第6頁/共14頁</div>
            </div>
            <div class="slide-title">🥇 競爭格局：我們的獨特優勢</div>
            <div style="width: 100%; max-width: 1000px;">
                <table style="width: 100%; border-collapse: collapse; margin: 40px 0;">
                    <thead>
                        <tr style="background: #f5f5f5;">
                            <th style="padding: 15px; border: 1px solid #ddd; font-size: 18px; color: #333;">維度</th>
                            <th style="padding: 15px; border: 1px solid #ddd; font-size: 18px; color: #333;">傳統模式</th>
                            <th style="padding: 15px; border: 1px solid #ddd; font-size: 18px; color: #333;">泛用平台</th>
                            <th style="padding: 15px; border: 1px solid #ddd; font-size: 18px; color: #1B5E20; font-weight: bold;">SHUAN-Q</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">專業化程度</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px; color: #1B5E20;">✅</td>
                        </tr>
                        <tr style="background: #f9f9f9;">
                            <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">用戶體驗</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">⚠️</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px; color: #1B5E20;">✅</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">商城一體化</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px; color: #1B5E20;">✅</td>
                        </tr>
                        <tr style="background: #f9f9f9;">
                            <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">技術先進性</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">⚠️</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px; color: #1B5E20;">✅</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">收入多元化</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px;">❌</td>
                            <td style="padding: 12px; border: 1px solid #ddd; text-align: center; font-size: 20px; color: #1B5E20;">✅</td>
                        </tr>
                    </tbody>
                </table>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px; margin-top: 40px;">
                    <div style="background: rgba(27, 94, 32, 0.1); padding: 20px; border-radius: 10px;">
                        <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">🎯 垂直領域專業化</div>
                        <div style="color: #666;">深度理解台球生態</div>
                    </div>
                    <div style="background: rgba(27, 94, 32, 0.1); padding: 20px; border-radius: 10px;">
                        <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">💻 技術架構先進</div>
                        <div style="color: #666;">React Native跨平台</div>
                    </div>
                    <div style="background: rgba(27, 94, 32, 0.1); padding: 20px; border-radius: 10px;">
                        <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">🛒 教練+商城一體</div>
                        <div style="color: #666;">形成業務閉環</div>
                    </div>
                    <div style="background: rgba(27, 94, 32, 0.1); padding: 20px; border-radius: 10px;">
                        <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">🚀 先發優勢明顯</div>
                        <div style="color: #666;">市場幾乎空白</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Target Users -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第7頁/共14頁</div>
            </div>
            <div class="slide-title">👥 目標用戶：精準定位</div>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; width: 100%; max-width: 1200px;">
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">學員用戶</div>
                    <div style="color: #666; margin-bottom: 20px;">B2C</div>
                    <div style="font-size: 48px; margin: 20px 0;">👨‍💼</div>
                    <div style="color: #333; margin-bottom: 15px;">
                        <strong>白領學生</strong><br>
                        18-45歲<br>
                        5K-20K月收入
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="color: #d32f2f; font-weight: bold; margin-bottom: 8px;">痛點：</div>
                        <div style="font-size: 14px; color: #666;">找教練難<br>價格不透明<br>預約繁瑣</div>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                        <div style="color: #1B5E20; font-weight: bold; margin-bottom: 8px;">需求：</div>
                        <div style="font-size: 14px; color: #666;">便捷約課<br>透明定價<br>優質教學</div>
                    </div>
                </div>
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">教練用戶</div>
                    <div style="color: #666; margin-bottom: 20px;">B2B</div>
                    <div style="font-size: 48px; margin: 20px 0;">👨‍🏫</div>
                    <div style="color: #333; margin-bottom: 15px;">
                        <strong>專業教練</strong><br>
                        退役球員<br>
                        球房教練
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="color: #d32f2f; font-weight: bold; margin-bottom: 8px;">痛點：</div>
                        <div style="font-size: 14px; color: #666;">獲客難<br>收入不穩定<br>缺乏展示</div>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                        <div style="color: #1B5E20; font-weight: bold; margin-bottom: 8px;">需求：</div>
                        <div style="font-size: 14px; color: #666;">穩定收入<br>專業展示<br>學員資源</div>
                    </div>
                </div>
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">供應商用戶</div>
                    <div style="color: #666; margin-bottom: 20px;">B2B</div>
                    <div style="font-size: 48px; margin: 20px 0;">🏪</div>
                    <div style="color: #333; margin-bottom: 15px;">
                        <strong>品牌商家</strong><br>
                        台球設備商<br>
                        配件供應商
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="color: #d32f2f; font-weight: bold; margin-bottom: 8px;">痛點：</div>
                        <div style="font-size: 14px; color: #666;">銷路窄<br>獲客成本高<br>庫存壓力</div>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                        <div style="color: #1B5E20; font-weight: bold; margin-bottom: 8px;">需求：</div>
                        <div style="font-size: 14px; color: #666;">銷售渠道<br>精準流量<br>降低庫存</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Slide 8: Product Architecture -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第8頁/共14頁</div>
            </div>
            <div class="slide-title">🏗️ 產品架構：技術驅動創新</div>
            <div style="width: 100%; max-width: 1000px; text-align: center;">
                <div style="background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); border-radius: 15px; padding: 40px; margin: 20px 0;">
                    <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">移動端應用 (React Native)</div>
                    <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                        <div style="background: #1B5E20; color: white; padding: 15px 30px; border-radius: 10px;">iOS端</div>
                        <div style="background: #1B5E20; color: white; padding: 15px 30px; border-radius: 10px;">Android端</div>
                    </div>
                    <div style="height: 2px; background: #1B5E20; margin: 20px 0;"></div>
                    <div style="font-size: 20px; font-weight: bold; color: #333; margin: 20px 0;">API層</div>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0;">
                        <div style="background: rgba(27, 94, 32, 0.1); padding: 15px; border-radius: 8px;">
                            <div style="font-weight: bold; color: #1B5E20;">用戶管理</div>
                            <div style="font-size: 14px; color: #666;">教練管理</div>
                        </div>
                        <div style="background: rgba(27, 94, 32, 0.1); padding: 15px; border-radius: 8px;">
                            <div style="font-weight: bold; color: #1B5E20;">預約系統</div>
                            <div style="font-size: 14px; color: #666;">支付系統</div>
                        </div>
                        <div style="background: rgba(27, 94, 32, 0.1); padding: 15px; border-radius: 8px;">
                            <div style="font-weight: bold; color: #1B5E20;">商城系統</div>
                            <div style="font-size: 14px; color: #666;">訂單管理</div>
                        </div>
                    </div>
                    <div style="height: 2px; background: #1B5E20; margin: 20px 0;"></div>
                    <div style="font-size: 20px; font-weight: bold; color: #333; margin: 20px 0;">數據層</div>
                    <div style="background: #333; color: white; padding: 15px; border-radius: 10px;">雲端數據庫</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px; margin-top: 30px;">
                    <div style="text-align: left;">
                        <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 15px;">技術亮點：</div>
                        <div style="color: #333; line-height: 1.8;">
                            ✅ 跨平台一體化開發<br>
                            ✅ 模組化微服務架構<br>
                            ✅ TypeScript類型安全<br>
                            ✅ 現代化技術棧<br>
                            ✅ 雲端部署支持高併發
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: center;">
                        <div style="font-size: 120px; opacity: 0.3;">⚙️</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Completed Features -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第9頁/共14頁</div>
            </div>
            <div class="slide-title">✅ 已完成功能：實力已驗證</div>
            <div style="width: 100%; max-width: 1200px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin: 40px 0;">
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">🔐</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">用戶認證系統</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            手機號驗證登入<br>
                            多角色權限管理
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">👨‍🏫</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">教練管理系統</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            資料完整管理<br>
                            搜尋篩選功能
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">📅</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">預約管理系統</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            智能預約調度<br>
                            狀態實時更新
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">📦</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">課程包管理</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            創建查詢分類<br>
                            價格靈活設定
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">💳</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">訂單支付系統</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            訂單創建管理<br>
                            模擬支付完成
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 15px; padding: 25px; text-align: center;">
                        <div style="font-size: 32px; margin-bottom: 15px;">📱</div>
                        <div style="font-size: 18px; font-weight: bold; color: #1B5E20; margin-bottom: 10px;">移動端應用</div>
                        <div style="color: #666; font-size: 14px; line-height: 1.6;">
                            iOS/Android<br>
                            原生用戶體驗
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 40px;">
                    <div style="background: #f5f5f5; border-radius: 25px; padding: 15px; display: inline-block; margin-bottom: 20px;">
                        <div style="width: 400px; height: 20px; background: #e0e0e0; border-radius: 10px; position: relative;">
                            <div style="width: 90%; height: 100%; background: linear-gradient(90deg, #1B5E20, #2E7D32); border-radius: 10px;"></div>
                            <div style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%); color: #333; font-weight: bold;">90%</div>
                        </div>
                    </div>
                    <div style="font-size: 18px; color: #333;">
                        <strong>核心功能已完成90%</strong><br>
                        <span style="color: #666;">剩餘工作：商城系統 + 真實支付接入</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: Mall System Design -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第10頁/共14頁</div>
            </div>
            <div class="slide-title">🛍️ 商城系統設計：增值收入引擎</div>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 40px; width: 100%; max-width: 1200px;">
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                    <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">🎯 台球用品專區</div>
                    <div style="color: #666; line-height: 1.8;">
                        • 球桿、球檯、配件<br>
                        • 專業品牌代理<br>
                        • 教練推薦商品<br>
                        • 會員專享折扣
                    </div>
                </div>
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                    <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">📚 教學資源</div>
                    <div style="color: #666; line-height: 1.8;">
                        • 視頻教程付費觀看<br>
                        • 電子書籍下載<br>
                        • 訓練計劃模板<br>
                        • 技巧分析工具
                    </div>
                </div>
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                    <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">🏆 賽事周邊</div>
                    <div style="color: #666; line-height: 1.8;">
                        • 比賽報名費<br>
                        • 紀念商品<br>
                        • 獎品禮包<br>
                        • VIP觀賽席位
                    </div>
                </div>
                <div style="background: #f8f9fa; border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                    <div style="font-size: 20px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">💎 會員服務</div>
                    <div style="color: #666; line-height: 1.8;">
                        • 年費會員制<br>
                        • 積分兌換商品<br>
                        • 生日專享禮包<br>
                        • 定制服務
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <div style="background: linear-gradient(135deg, #1B5E20, #2E7D32); color: white; padding: 20px; border-radius: 15px; display: inline-block;">
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">預期商城GMV</div>
                    <div style="font-size: 20px;">第一年：500萬 | 第二年：1200萬 | 第三年：2500萬</div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Development Roadmap -->
        <div class="slide">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第11頁/共14頁</div>
            </div>
            <div class="slide-title">🗺️ 產品路線圖：按步實施</div>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">Q1 2024</div>
                    <div class="timeline-stage">✅ 已完成</div>
                    <div class="timeline-desc">
                        • 核心預約系統<br>
                        • 教練管理<br>
                        • 移動端APP<br>
                        • 基礎功能測試
                    </div>
                    <div class="arrow">➤</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Q2 2024</div>
                    <div class="timeline-stage">🚧 進行中</div>
                    <div class="timeline-desc">
                        • 商城系統開發<br>
                        • 支付系統集成<br>
                        • 用戶體驗優化<br>
                        • Beta版本測試
                    </div>
                    <div class="arrow">➤</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Q3 2024</div>
                    <div class="timeline-stage">📋 計劃中</div>
                    <div class="timeline-desc">
                        • 正式上線<br>
                        • 市場推廣<br>
                        • 教練招募<br>
                        • 用戶拓展
                    </div>
                    <div class="arrow">➤</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Q4 2024</div>
                    <div class="timeline-stage">🎯 目標</div>
                    <div class="timeline-desc">
                        • 商城正式運營<br>
                        • AI智能推薦<br>
                        • 數據分析系統<br>
                        • 盈利模式驗證
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 12: Business Model -->
        <div class="slide white-bg">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第12頁/共14頁</div>
            </div>
            <div class="slide-title">💰 商業模式：多元化收入結構</div>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 40px; width: 100%; max-width: 1200px;">
                <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 20px; padding: 30px; text-align: center;">
                    <div style="font-size: 32px; margin-bottom: 20px;">🎯</div>
                    <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 15px;">預約服務費</div>
                    <div style="font-size: 20px; color: #333; margin-bottom: 15px;">每筆訂單5-8%</div>
                    <div style="color: #666; line-height: 1.6;">
                        • 教練課程預約<br>
                        • 平台撮合服務<br>
                        • 信用保障費用
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 20px; padding: 30px; text-align: center;">
                    <div style="font-size: 32px; margin-bottom: 20px;">🛍️</div>
                    <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 15px;">商城銷售額</div>
                    <div style="font-size: 20px; color: #333; margin-bottom: 15px;">GMV的15-25%</div>
                    <div style="color: #666; line-height: 1.6;">
                        • 台球用品銷售<br>
                        • 教學資源付費<br>
                        • 會員服務費
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 20px; padding: 30px; text-align: center;">
                    <div style="font-size: 32px; margin-bottom: 20px;">📱</div>
                    <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 15px;">增值服務</div>
                    <div style="font-size: 20px; color: #333; margin-bottom: 15px;">年收入10-20%</div>
                    <div style="color: #666; line-height: 1.6;">
                        • 教練認證費<br>
                        • 廣告推廣費<br>
                        • 數據分析服務
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%); border: 2px solid #1B5E20; border-radius: 20px; padding: 30px; text-align: center;">
                    <div style="font-size: 32px; margin-bottom: 20px;">🤝</div>
                    <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 15px;">合作分成</div>
                    <div style="font-size: 20px; color: #333; margin-bottom: 15px;">合作收入30%</div>
                    <div style="color: #666; line-height: 1.6;">
                        • 球館租借分成<br>
                        • 賽事組織分成<br>
                        • 品牌合作收入
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Financial Projection -->
        <div class="slide">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第13頁/共14頁</div>
            </div>
            <div class="slide-title">📊 財務預測：3年盈利計劃</div>
            <div style="width: 100%; max-width: 1200px;">
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 40px;">
                    <div style="text-align: center; color: #1B5E20;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">項目</div>
                    </div>
                    <div style="text-align: center; color: #1B5E20;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">2024年</div>
                    </div>
                    <div style="text-align: center; color: #1B5E20;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">2025年</div>
                    </div>
                    <div style="text-align: center; color: #1B5E20;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">2026年</div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 10px;">收入(萬元)</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold;">180</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold;">520</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold;">1200</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 10px;">成本(萬元)</div>
                    <div style="padding: 15px; background: #fff2f0; border-radius: 10px; text-align: center;">120</div>
                    <div style="padding: 15px; background: #fff2f0; border-radius: 10px; text-align: center;">280</div>
                    <div style="padding: 15px; background: #fff2f0; border-radius: 10px; text-align: center;">480</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 10px;">淨利潤(萬元)</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold; color: #1B5E20;">60</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold; color: #1B5E20;">240</div>
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; text-align: center; font-weight: bold; color: #1B5E20;">720</div>
                </div>
                <div style="text-align: center; margin-top: 40px;">
                    <div style="background: linear-gradient(135deg, #1B5E20, #2E7D32); color: white; padding: 30px; border-radius: 20px; display: inline-block;">
                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 15px;">投資回報預期</div>
                        <div style="font-size: 32px; font-weight: bold;">270%</div>
                        <div style="font-size: 16px; margin-top: 10px;">3年內實現投資回報，第4年開始穩定分紅</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 24: Final Call to Action -->
        <div class="slide">
            <div class="slide-header">
                <div class="logo">SQ</div>
                <div class="page-number">第14頁/共14頁</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 48px; font-weight: bold; color: #1B5E20; margin-bottom: 30px;">🤝 攜手共創：下一個獨角獸</div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 40px; margin: 50px 0; max-width: 1000px;">
                    <div style="background: rgba(255, 255, 255, 0.1); border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                        <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">我們提供</div>
                        <div style="color: #ccc; line-height: 2;">
                            🎯 創新商業模式<br>
                            🚀 優秀執行團隊<br>
                            💻 完整技術方案<br>
                            📈 清晰盈利路徑
                        </div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.1); border: 2px solid #1B5E20; border-radius: 15px; padding: 30px;">
                        <div style="font-size: 24px; font-weight: bold; color: #1B5E20; margin-bottom: 20px;">期待您的</div>
                        <div style="color: #ccc; line-height: 2;">
                            💰 資金支持<br>
                            🎓 行業經驗<br>
                            🔗 資源對接<br>
                            🤝 戰略指導
                        </div>
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #1B5E20, #2E7D32); padding: 40px; border-radius: 20px; margin: 40px 0; color: white;">
                    <div style="font-size: 32px; font-weight: bold; margin-bottom: 20px;">立即行動</div>
                    <div style="font-size: 18px; line-height: 1.8;">
                        🔥 融資窗口期：2024年Q1-Q2<br>
                        ⭐ 預期完成時間：3個月内<br>
                        🎯 資金到位即可啟動商城開發<br>
                        🚀 6個月内實現商城系統上線
                    </div>
                </div>
                <div style="font-size: 28px; font-weight: bold; color: #1B5E20; margin-top: 40px;">
                    "從台球起步，打造技能教培生態平台<br>
                    投資SHUAN-Q，就是投資下一個時代"
                </div>
            </div>
        </div>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">14</span>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prev-btn" onclick="previousSlide()">◀ 上一頁</button>
        <button class="nav-btn" id="next-btn" onclick="nextSlide()">下一頁 ▶</button>
        <button class="nav-btn" onclick="downloadPPT()" style="background: #ff6b35;">📥 下載PPT</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        function downloadPPT() {
            // Create download link for the HTML file
            const element = document.createElement('a');
            const file = new Blob([document.documentElement.outerHTML], {type: 'text/html'});
            element.href = URL.createObjectURL(file);
            element.download = 'SHUAN-Q融資計劃書.html';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // Initialize
        document.getElementById('total-slides').textContent = totalSlides;
        showSlide(0);
    </script>
</body>
</html> 